#!/usr/bin/env python3
"""
Comprehensive Test Suite for Idea Refiner
==========================================

This script provides systematic testing of the idea refinement functionality
following the user's requirements for quality assessment and iterative improvement.

Test Phases:
1. Initial Testing - Run refiner on ideas.txt and capture outputs
2. Quality Assessment - Evaluate refined outputs for clarity, specificity, actionability
3. Debug Analysis - Enable debug logging to identify issues
4. Iterative Improvement - Make targeted improvements based on results
5. Validation - Continue testing until consistent high-quality output
"""

import subprocess
import sys
import json
import time
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class QualityMetrics:
    """Metrics for evaluating refined idea quality."""
    clarity_score: float  # 0-10: How clear and specific is the refined idea
    actionability_score: float  # 0-10: How actionable are the acceptance criteria
    technical_detail_score: float  # 0-10: Level of concrete technical details
    domain_context_score: float  # 0-10: Payment processing domain appropriateness
    generic_content_penalty: float  # 0-10: Penalty for generic statements
    overall_score: float  # Calculated overall quality score


@dataclass
class TestResult:
    """Result of testing a single idea."""
    idea_number: int
    original_idea: str
    refined_title: str
    processing_time: float
    quality_metrics: QualityMetrics
    issues_found: List[str]
    debug_files: List[Path]


class IdeaRefinerTester:
    """Comprehensive tester for idea refinement functionality."""

    def __init__(self, test_run_id: Optional[str] = None):
        self.test_run_id = test_run_id or datetime.now().strftime("%Y%m%d_%H%M%S")
        self.test_dir = Path(f"test_results_{self.test_run_id}")
        self.test_dir.mkdir(exist_ok=True)

        # Test configuration
        self.models_to_test = ["qwen3"]  # Can expand to test multiple models
        self.debug_enabled = True

        print(f"🧪 Idea Refiner Test Suite - Run ID: {self.test_run_id}")
        print(f"📁 Test results will be saved to: {self.test_dir}")
        print("=" * 60)

    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run the complete test suite following the systematic approach."""

        # Phase 1: Initial Testing
        print("\n🚀 Phase 1: Initial Testing")
        print("-" * 30)
        initial_results = self.run_initial_testing()

        # Phase 2: Quality Assessment
        print("\n📊 Phase 2: Quality Assessment")
        print("-" * 30)
        quality_analysis = self.assess_quality(initial_results)

        # Phase 3: Debug Analysis
        print("\n🔍 Phase 3: Debug Analysis")
        print("-" * 30)
        debug_analysis = self.analyze_debug_logs()

        # Phase 4: Generate Improvement Recommendations
        print("\n💡 Phase 4: Improvement Recommendations")
        print("-" * 30)
        recommendations = self.generate_recommendations(quality_analysis, debug_analysis)

        # Phase 5: Summary Report
        print("\n📋 Phase 5: Test Summary")
        print("-" * 30)
        summary = self.generate_test_summary(initial_results, quality_analysis, debug_analysis, recommendations)

        return summary

    def run_initial_testing(self) -> List[TestResult]:
        """Phase 1: Run the idea refiner and capture outputs."""

        # Verify required files exist
        if not self.verify_required_files():
            raise RuntimeError("Missing required files for testing")

        # Clean previous outputs
        output_dir = Path("refined_tickets")
        if output_dir.exists():
            shutil.rmtree(output_dir)

        debug_dir = Path("llm_debug_logs")
        if debug_dir.exists():
            shutil.rmtree(debug_dir)

        # Run the idea refiner with debug logging enabled
        print("Running idea refiner with debug logging enabled...")
        start_time = time.time()

        cmd = [
            "python3", "idea_refiner.py",
            "--ideas", "ideas.txt",
            "--jira_tickets", "jira_tickets.json",
            "--enriched_tickets", "enriched_tickets_clean.json",
            "--tree_log", "tree.log",
            "--git_log", "gitlog.log",
            "--output_dir", str(output_dir),
            "--model", "qwen3",
            "--log_level", "DEBUG",
            "--debug_dir", str(debug_dir)
        ]

        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=600)
            processing_time = time.time() - start_time

            print(f"✅ Idea refiner completed successfully in {processing_time:.2f} seconds")

            # Save raw output for analysis
            self.save_raw_output(result, processing_time)

            # Parse and analyze results
            test_results = self.parse_test_results(output_dir, debug_dir)

            return test_results

        except subprocess.CalledProcessError as e:
            print(f"❌ Idea refiner failed with error: {e}")
            print(f"STDOUT: {e.stdout}")
            print(f"STDERR: {e.stderr}")

            # Save error output for analysis
            self.save_error_output(e)
            raise

        except subprocess.TimeoutExpired:
            print("❌ Idea refiner timed out after 10 minutes")
            raise

    def verify_required_files(self) -> bool:
        """Verify all required input files exist."""
        required_files = [
            "ideas.txt",
            "jira_tickets.json",
            "enriched_tickets_clean.json",
            "tree.log",
            "gitlog.log"
        ]

        missing_files = []
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)

        if missing_files:
            print(f"❌ Missing required files: {', '.join(missing_files)}")
            return False

        print(f"✅ All required files found: {', '.join(required_files)}")
        return True

    def save_raw_output(self, result: subprocess.CompletedProcess, processing_time: float):
        """Save raw subprocess output for analysis."""
        output_file = self.test_dir / "raw_output.txt"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"=== IDEA REFINER RAW OUTPUT ===\n")
            f.write(f"Processing Time: {processing_time:.2f} seconds\n")
            f.write(f"Return Code: {result.returncode}\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            f.write("=" * 50 + "\n\n")
            f.write("STDOUT:\n")
            f.write(result.stdout)
            f.write("\n\nSTDERR:\n")
            f.write(result.stderr)

    def save_error_output(self, error: subprocess.CalledProcessError):
        """Save error output for analysis."""
        error_file = self.test_dir / "error_output.txt"
        with open(error_file, 'w', encoding='utf-8') as f:
            f.write(f"=== IDEA REFINER ERROR OUTPUT ===\n")
            f.write(f"Return Code: {error.returncode}\n")
            f.write(f"Timestamp: {datetime.now().isoformat()}\n")
            f.write("=" * 50 + "\n\n")
            f.write("STDOUT:\n")
            f.write(error.stdout or "No stdout")
            f.write("\n\nSTDERR:\n")
            f.write(error.stderr or "No stderr")

    def parse_test_results(self, output_dir: Path, debug_dir: Path) -> List[TestResult]:
        """Parse generated outputs and debug logs into test results."""
        test_results = []

        # Load original ideas
        with open("ideas.txt", 'r', encoding='utf-8') as f:
            ideas = [line.strip() for line in f if line.strip()]

        # Remove numbered prefixes
        import re
        clean_ideas = []
        for idea in ideas:
            clean_idea = re.sub(r'^\d+\.\s*', '', idea)
            clean_ideas.append(clean_idea)

        # Find debug directories (timestamped)
        debug_dirs = []
        if debug_dir.exists():
            debug_dirs = [d for d in debug_dir.iterdir() if d.is_dir()]

        latest_debug_dir = None
        if debug_dirs:
            latest_debug_dir = max(debug_dirs, key=lambda d: d.stat().st_mtime)

        # Process each idea
        for i, idea in enumerate(clean_ideas, 1):
            print(f"Analyzing results for idea {i}: {idea[:50]}...")

            # Find corresponding output file
            output_files = list(output_dir.glob(f"ticket_{i:02d}_*.txt"))
            output_file = output_files[0] if output_files else None

            # Find debug files for this idea
            debug_files = []
            if latest_debug_dir:
                debug_files = list(latest_debug_dir.glob(f"idea_{i:02d}_*"))

            # Parse processing time from debug logs
            processing_time = 0.0
            if latest_debug_dir:
                metadata_file = latest_debug_dir / "run_metadata.json"
                if metadata_file.exists():
                    try:
                        with open(metadata_file, 'r') as f:
                            metadata = json.load(f)
                            processing_times = metadata.get('processing_times', {})
                            processing_time = processing_times.get(f'idea_{i}', 0.0)
                    except Exception:
                        pass

            # Parse refined title from output file
            refined_title = "Unknown"
            if output_file and output_file.exists():
                try:
                    with open(output_file, 'r', encoding='utf-8') as f:
                        first_line = f.readline().strip()
                        if first_line.startswith('# '):
                            refined_title = first_line[2:]
                except Exception:
                    pass

            # Assess quality (placeholder - will be implemented in assess_quality)
            quality_metrics = QualityMetrics(
                clarity_score=0.0,
                actionability_score=0.0,
                technical_detail_score=0.0,
                domain_context_score=0.0,
                generic_content_penalty=0.0,
                overall_score=0.0
            )

            test_result = TestResult(
                idea_number=i,
                original_idea=idea,
                refined_title=refined_title,
                processing_time=processing_time,
                quality_metrics=quality_metrics,
                issues_found=[],
                debug_files=debug_files
            )

            test_results.append(test_result)

        print(f"✅ Parsed {len(test_results)} test results")
        return test_results

    def assess_quality(self, test_results: List[TestResult]) -> Dict[str, Any]:
        """Phase 2: Assess quality of refined outputs."""

        print("Evaluating quality of refined ideas...")

        quality_analysis = {
            "individual_scores": [],
            "aggregate_metrics": {},
            "quality_issues": [],
            "recommendations": []
        }

        total_scores = {
            "clarity": 0.0,
            "actionability": 0.0,
            "technical_detail": 0.0,
            "domain_context": 0.0,
            "generic_penalty": 0.0
        }

        for result in test_results:
            print(f"  Evaluating idea {result.idea_number}: {result.refined_title}")

            # Load the refined ticket content
            output_dir = Path("refined_tickets")
            output_files = list(output_dir.glob(f"ticket_{result.idea_number:02d}_*.txt"))

            if not output_files:
                print(f"    ❌ No output file found for idea {result.idea_number}")
                continue

            with open(output_files[0], 'r', encoding='utf-8') as f:
                content = f.read()

            # Evaluate quality metrics
            metrics = self.evaluate_content_quality(content, result.original_idea)
            result.quality_metrics = metrics

            # Track aggregate scores
            total_scores["clarity"] += metrics.clarity_score
            total_scores["actionability"] += metrics.actionability_score
            total_scores["technical_detail"] += metrics.technical_detail_score
            total_scores["domain_context"] += metrics.domain_context_score
            total_scores["generic_penalty"] += metrics.generic_content_penalty

            # Identify specific issues
            issues = self.identify_quality_issues(content, metrics)
            result.issues_found = issues

            quality_analysis["individual_scores"].append({
                "idea_number": result.idea_number,
                "original_idea": result.original_idea,
                "refined_title": result.refined_title,
                "metrics": metrics,
                "issues": issues
            })

        # Calculate aggregate metrics
        num_results = len(test_results)
        if num_results > 0:
            quality_analysis["aggregate_metrics"] = {
                "average_clarity": total_scores["clarity"] / num_results,
                "average_actionability": total_scores["actionability"] / num_results,
                "average_technical_detail": total_scores["technical_detail"] / num_results,
                "average_domain_context": total_scores["domain_context"] / num_results,
                "average_generic_penalty": total_scores["generic_penalty"] / num_results,
                "overall_average": sum(r.quality_metrics.overall_score for r in test_results) / num_results
            }

        # Save quality analysis
        self.save_quality_analysis(quality_analysis)

        return quality_analysis

    def evaluate_content_quality(self, content: str, original_idea: str) -> QualityMetrics:
        """Evaluate the quality of refined content based on user preferences."""

        # Initialize scores
        clarity_score = 0.0
        actionability_score = 0.0
        technical_detail_score = 0.0
        domain_context_score = 0.0
        generic_penalty = 0.0

        content_lower = content.lower()

        # 1. Clarity and Specificity (0-10)
        clarity_indicators = [
            "specific", "exactly", "precisely", "must", "shall", "will",
            "iso 8583", "data element", "de3", "de7", "processing code",
            "authorization", "clearing", "settlement", "reversal"
        ]
        clarity_score = min(10.0, sum(2.0 for indicator in clarity_indicators if indicator in content_lower))

        # 2. Actionability of Acceptance Criteria (0-10)
        actionable_patterns = [
            r"when.*then", r"given.*when.*then", r"must.*verify", r"shall.*validate",
            r"system.*should", r"component.*must", r"test.*verify", r"validate.*that"
        ]
        import re
        actionability_score = min(10.0, sum(2.0 for pattern in actionable_patterns
                                          if re.search(pattern, content_lower)))

        # Bonus for specific technical criteria
        if "acceptance criteria" in content_lower:
            actionability_score += 2.0

        # 3. Technical Detail Score (0-10)
        technical_indicators = [
            "kafka", "sqs", "database", "migration", "schema", "component test",
            "file path", "java", "yaml", "json", "api", "endpoint", "service",
            "visa", "mastercard", "amex", "jcb", "diners", "unionpay"
        ]
        technical_detail_score = min(10.0, sum(1.5 for indicator in technical_indicators
                                             if indicator in content_lower))

        # 4. Domain Context Score (0-10)
        domain_indicators = [
            "payment processing", "card scheme", "authorization", "clearing",
            "settlement", "reconciliation", "iso 8583", "terminal", "acquirer",
            "issuer", "transaction", "reversal", "void", "capture", "refund"
        ]
        domain_context_score = min(10.0, sum(1.0 for indicator in domain_indicators
                                           if indicator in content_lower))

        # 5. Generic Content Penalty (0-10, higher is worse)
        generic_phrases = [
            "feature is implemented and tested",
            "what are the performance requirements",
            "to be determined",
            "as needed",
            "appropriate testing",
            "standard practices",
            "best practices",
            "industry standards",
            "follow guidelines",
            "implement feature"
        ]
        generic_penalty = min(10.0, sum(2.0 for phrase in generic_phrases
                                      if phrase in content_lower))

        # Calculate overall score (penalize generic content)
        overall_score = (clarity_score + actionability_score + technical_detail_score +
                        domain_context_score - generic_penalty) / 4.0
        overall_score = max(0.0, min(10.0, overall_score))

        return QualityMetrics(
            clarity_score=clarity_score,
            actionability_score=actionability_score,
            technical_detail_score=technical_detail_score,
            domain_context_score=domain_context_score,
            generic_content_penalty=generic_penalty,
            overall_score=overall_score
        )

    def identify_quality_issues(self, content: str, metrics: QualityMetrics) -> List[str]:
        """Identify specific quality issues in the content."""
        issues = []

        content_lower = content.lower()

        # Check for generic statements
        if "to be determined" in content_lower:
            issues.append("Contains generic 'to be determined' statements")

        if "feature is implemented and tested" in content_lower:
            issues.append("Contains generic 'feature is implemented and tested' statement")

        if "what are the performance requirements" in content_lower:
            issues.append("Contains generic performance requirements question")

        # Check for lack of specificity
        if metrics.clarity_score < 5.0:
            issues.append("Low clarity score - needs more specific language")

        if metrics.actionability_score < 5.0:
            issues.append("Low actionability score - acceptance criteria not specific enough")

        if metrics.technical_detail_score < 5.0:
            issues.append("Low technical detail score - needs more concrete implementation details")

        if metrics.domain_context_score < 5.0:
            issues.append("Low domain context score - needs more payment processing terminology")

        if metrics.generic_content_penalty > 5.0:
            issues.append("High generic content penalty - too many generic statements")

        # Check for missing sections
        if "acceptance criteria" not in content_lower:
            issues.append("Missing acceptance criteria section")

        if "business value" not in content_lower:
            issues.append("Missing business value section")

        if "technical design" not in content_lower:
            issues.append("Missing technical design section")

        return issues

    def save_quality_analysis(self, quality_analysis: Dict[str, Any]):
        """Save quality analysis to file."""
        analysis_file = self.test_dir / "quality_analysis.json"
        with open(analysis_file, 'w', encoding='utf-8') as f:
            # Convert QualityMetrics objects to dicts for JSON serialization
            serializable_analysis = quality_analysis.copy()
            for score_data in serializable_analysis["individual_scores"]:
                if "metrics" in score_data:
                    metrics = score_data["metrics"]
                    score_data["metrics"] = {
                        "clarity_score": metrics.clarity_score,
                        "actionability_score": metrics.actionability_score,
                        "technical_detail_score": metrics.technical_detail_score,
                        "domain_context_score": metrics.domain_context_score,
                        "generic_content_penalty": metrics.generic_content_penalty,
                        "overall_score": metrics.overall_score
                    }

            json.dump(serializable_analysis, f, indent=2, ensure_ascii=False)

        print(f"📊 Quality analysis saved to: {analysis_file}")

    def analyze_debug_logs(self) -> Dict[str, Any]:
        """Phase 3: Analyze debug logs to identify issues."""

        print("Analyzing debug logs for issues...")

        debug_analysis = {
            "llm_response_issues": [],
            "json_parsing_issues": [],
            "processing_errors": [],
            "performance_metrics": {},
            "recommendations": []
        }

        # Find latest debug directory
        debug_dir = Path("llm_debug_logs")
        if not debug_dir.exists():
            print("❌ No debug logs found")
            return debug_analysis

        debug_dirs = [d for d in debug_dir.iterdir() if d.is_dir()]
        if not debug_dirs:
            print("❌ No debug log directories found")
            return debug_analysis

        latest_debug_dir = max(debug_dirs, key=lambda d: d.stat().st_mtime)
        print(f"📁 Analyzing debug logs from: {latest_debug_dir}")

        # Analyze metadata
        metadata_file = latest_debug_dir / "run_metadata.json"
        if metadata_file.exists():
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
                debug_analysis["performance_metrics"] = {
                    "total_runtime": metadata.get("total_runtime_seconds", 0),
                    "ideas_processed": metadata.get("ideas_processed", 0),
                    "error_count": len(metadata.get("errors", [])),
                    "processing_times": metadata.get("processing_times", {})
                }

        # Analyze individual idea logs
        for idea_file in latest_debug_dir.glob("idea_*_response.txt"):
            idea_num = idea_file.stem.split('_')[1]

            # Check for response issues
            with open(idea_file, 'r', encoding='utf-8') as f:
                response_content = f.read()

                if len(response_content) < 500:
                    debug_analysis["llm_response_issues"].append(
                        f"Idea {idea_num}: Very short response ({len(response_content)} chars)"
                    )

                if "error" in response_content.lower():
                    debug_analysis["llm_response_issues"].append(
                        f"Idea {idea_num}: Response contains error messages"
                    )

        # Check for JSON parsing issues
        for parsed_file in latest_debug_dir.glob("idea_*_parsed.json"):
            try:
                with open(parsed_file, 'r') as f:
                    parsed_data = json.load(f)

                    # Check if fallback parsing was used
                    if "extracted_sections" in parsed_data:
                        sections = parsed_data["extracted_sections"]
                        if "json_response" not in sections or not sections["json_response"]:
                            idea_num = parsed_file.stem.split('_')[1]
                            debug_analysis["json_parsing_issues"].append(
                                f"Idea {idea_num}: JSON parsing may have failed, used fallback"
                            )
            except Exception as e:
                debug_analysis["json_parsing_issues"].append(
                    f"Failed to parse {parsed_file}: {e}"
                )

        # Check for error files
        for error_file in latest_debug_dir.glob("idea_*_error.json"):
            with open(error_file, 'r') as f:
                error_data = json.load(f)
                debug_analysis["processing_errors"].append(error_data)

        # Save debug analysis
        debug_analysis_file = self.test_dir / "debug_analysis.json"
        with open(debug_analysis_file, 'w', encoding='utf-8') as f:
            json.dump(debug_analysis, f, indent=2, ensure_ascii=False)

        print(f"🔍 Debug analysis saved to: {debug_analysis_file}")
        return debug_analysis

    def generate_recommendations(self, quality_analysis: Dict[str, Any],
                               debug_analysis: Dict[str, Any]) -> List[str]:
        """Phase 4: Generate targeted improvement recommendations."""

        print("Generating improvement recommendations...")

        recommendations = []

        # Analyze aggregate quality metrics
        metrics = quality_analysis.get("aggregate_metrics", {})

        if metrics.get("average_clarity", 0) < 6.0:
            recommendations.append(
                "PROMPT IMPROVEMENT: Enhance prompt to request more specific, measurable language. "
                "Add examples of specific vs generic statements."
            )

        if metrics.get("average_actionability", 0) < 6.0:
            recommendations.append(
                "ACCEPTANCE CRITERIA: Improve prompt to generate more actionable acceptance criteria. "
                "Request 'Given-When-Then' format and specific validation steps."
            )

        if metrics.get("average_technical_detail", 0) < 6.0:
            recommendations.append(
                "TECHNICAL DETAIL: Enhance context injection to include more specific file paths, "
                "component names, and technical implementation details."
            )

        if metrics.get("average_domain_context", 0) < 6.0:
            recommendations.append(
                "DOMAIN KNOWLEDGE: Improve payment processing domain context in prompts. "
                "Add more scheme-specific terminology and industry standards."
            )

        if metrics.get("average_generic_penalty", 0) > 5.0:
            recommendations.append(
                "GENERIC CONTENT: Add explicit instructions to avoid generic statements like "
                "'to be determined', 'feature implemented and tested', etc."
            )

        # Analyze debug issues
        if debug_analysis.get("json_parsing_issues"):
            recommendations.append(
                "JSON PARSING: Improve JSON output reliability. Consider adding JSON schema "
                "validation examples in the prompt or implementing better error recovery."
            )

        if debug_analysis.get("llm_response_issues"):
            recommendations.append(
                "LLM RESPONSES: Address response quality issues. May need to adjust temperature, "
                "top_p parameters, or improve prompt structure."
            )

        # Performance recommendations
        perf_metrics = debug_analysis.get("performance_metrics", {})
        avg_time = sum(perf_metrics.get("processing_times", {}).values()) / max(1, len(perf_metrics.get("processing_times", {})))

        if avg_time > 30.0:
            recommendations.append(
                f"PERFORMANCE: Average processing time is {avg_time:.1f}s per idea. "
                "Consider optimizing context size or using a faster model."
            )

        # Save recommendations
        recommendations_file = self.test_dir / "recommendations.txt"
        with open(recommendations_file, 'w', encoding='utf-8') as f:
            f.write("IDEA REFINER IMPROVEMENT RECOMMENDATIONS\n")
            f.write("=" * 50 + "\n\n")
            for i, rec in enumerate(recommendations, 1):
                f.write(f"{i}. {rec}\n\n")

        print(f"💡 Recommendations saved to: {recommendations_file}")
        return recommendations

    def generate_test_summary(self, test_results: List[TestResult],
                            quality_analysis: Dict[str, Any],
                            debug_analysis: Dict[str, Any],
                            recommendations: List[str]) -> Dict[str, Any]:
        """Phase 5: Generate comprehensive test summary."""

        print("Generating test summary...")

        # Calculate summary statistics
        metrics = quality_analysis.get("aggregate_metrics", {})
        perf_metrics = debug_analysis.get("performance_metrics", {})

        summary = {
            "test_run_id": self.test_run_id,
            "timestamp": datetime.now().isoformat(),
            "test_configuration": {
                "model_used": "qwen3",
                "debug_enabled": self.debug_enabled,
                "ideas_tested": len(test_results)
            },
            "quality_summary": {
                "overall_average_score": metrics.get("overall_average", 0.0),
                "clarity_average": metrics.get("average_clarity", 0.0),
                "actionability_average": metrics.get("average_actionability", 0.0),
                "technical_detail_average": metrics.get("average_technical_detail", 0.0),
                "domain_context_average": metrics.get("average_domain_context", 0.0),
                "generic_penalty_average": metrics.get("average_generic_penalty", 0.0)
            },
            "performance_summary": {
                "total_runtime_seconds": perf_metrics.get("total_runtime", 0),
                "ideas_processed": perf_metrics.get("ideas_processed", 0),
                "error_count": perf_metrics.get("error_count", 0),
                "average_processing_time": sum(perf_metrics.get("processing_times", {}).values()) / max(1, len(perf_metrics.get("processing_times", {})))
            },
            "issues_summary": {
                "llm_response_issues": len(debug_analysis.get("llm_response_issues", [])),
                "json_parsing_issues": len(debug_analysis.get("json_parsing_issues", [])),
                "processing_errors": len(debug_analysis.get("processing_errors", []))
            },
            "recommendations_count": len(recommendations),
            "test_verdict": self.determine_test_verdict(metrics, debug_analysis)
        }

        # Save summary
        summary_file = self.test_dir / "test_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        # Generate human-readable report
        self.generate_human_readable_report(summary, test_results, recommendations)

        return summary

    def determine_test_verdict(self, metrics: Dict[str, Any], debug_analysis: Dict[str, Any]) -> str:
        """Determine overall test verdict based on quality and performance."""

        overall_score = metrics.get("overall_average", 0.0)
        error_count = debug_analysis.get("performance_metrics", {}).get("error_count", 0)

        if error_count > 0:
            return "FAILED - Processing errors occurred"
        elif overall_score >= 8.0:
            return "EXCELLENT - High quality output"
        elif overall_score >= 6.0:
            return "GOOD - Acceptable quality with room for improvement"
        elif overall_score >= 4.0:
            return "NEEDS_IMPROVEMENT - Quality below expectations"
        else:
            return "POOR - Significant quality issues"

    def generate_human_readable_report(self, summary: Dict[str, Any],
                                     test_results: List[TestResult],
                                     recommendations: List[str]):
        """Generate a human-readable test report."""

        report_file = self.test_dir / "test_report.md"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"# Idea Refiner Test Report\n\n")
            f.write(f"**Test Run ID:** {summary['test_run_id']}\n")
            f.write(f"**Timestamp:** {summary['timestamp']}\n")
            f.write(f"**Test Verdict:** {summary['test_verdict']}\n\n")

            f.write("## Test Configuration\n\n")
            config = summary['test_configuration']
            f.write(f"- **Model Used:** {config['model_used']}\n")
            f.write(f"- **Debug Enabled:** {config['debug_enabled']}\n")
            f.write(f"- **Ideas Tested:** {config['ideas_tested']}\n\n")

            f.write("## Quality Assessment\n\n")
            quality = summary['quality_summary']
            f.write(f"- **Overall Average Score:** {quality['overall_average_score']:.2f}/10\n")
            f.write(f"- **Clarity Average:** {quality['clarity_average']:.2f}/10\n")
            f.write(f"- **Actionability Average:** {quality['actionability_average']:.2f}/10\n")
            f.write(f"- **Technical Detail Average:** {quality['technical_detail_average']:.2f}/10\n")
            f.write(f"- **Domain Context Average:** {quality['domain_context_average']:.2f}/10\n")
            f.write(f"- **Generic Content Penalty:** {quality['generic_penalty_average']:.2f}/10\n\n")

            f.write("## Performance Summary\n\n")
            perf = summary['performance_summary']
            f.write(f"- **Total Runtime:** {perf['total_runtime_seconds']:.2f} seconds\n")
            f.write(f"- **Ideas Processed:** {perf['ideas_processed']}\n")
            f.write(f"- **Error Count:** {perf['error_count']}\n")
            f.write(f"- **Average Processing Time:** {perf['average_processing_time']:.2f} seconds per idea\n\n")

            f.write("## Individual Results\n\n")
            for result in test_results:
                f.write(f"### Idea {result.idea_number}: {result.refined_title}\n\n")
                f.write(f"**Original:** {result.original_idea}\n\n")
                f.write(f"**Quality Score:** {result.quality_metrics.overall_score:.2f}/10\n")
                f.write(f"**Processing Time:** {result.processing_time:.2f}s\n\n")

                if result.issues_found:
                    f.write("**Issues Found:**\n")
                    for issue in result.issues_found:
                        f.write(f"- {issue}\n")
                    f.write("\n")

            f.write("## Recommendations\n\n")
            for i, rec in enumerate(recommendations, 1):
                f.write(f"{i}. {rec}\n\n")

            f.write("## Next Steps\n\n")
            if summary['test_verdict'].startswith('EXCELLENT'):
                f.write("✅ The idea refiner is performing excellently. Consider:\n")
                f.write("- Running additional test cases\n")
                f.write("- Testing with different models\n")
                f.write("- Deploying to production\n")
            elif summary['test_verdict'].startswith('GOOD'):
                f.write("⚠️ The idea refiner is performing well but has room for improvement:\n")
                f.write("- Implement the recommendations above\n")
                f.write("- Run another test cycle\n")
                f.write("- Consider prompt engineering improvements\n")
            else:
                f.write("❌ The idea refiner needs significant improvement:\n")
                f.write("- Address all recommendations above\n")
                f.write("- Review prompt engineering\n")
                f.write("- Consider model parameter tuning\n")
                f.write("- Run iterative testing until quality improves\n")

        print(f"📋 Human-readable report saved to: {report_file}")


def main():
    """Main entry point for comprehensive testing."""

    print("🧪 IDEA REFINER COMPREHENSIVE TEST SUITE")
    print("=" * 60)
    print("This test suite will systematically evaluate the idea refinement")
    print("functionality following the user's requirements for quality assessment.")
    print()

    # Create tester instance
    tester = IdeaRefinerTester()

    try:
        # Run comprehensive test
        summary = tester.run_comprehensive_test()

        print("\n" + "=" * 60)
        print("🎯 TEST SUMMARY")
        print("=" * 60)
        print(f"Test Verdict: {summary['test_verdict']}")
        print(f"Overall Quality Score: {summary['quality_summary']['overall_average_score']:.2f}/10")
        print(f"Ideas Processed: {summary['performance_summary']['ideas_processed']}")
        print(f"Total Runtime: {summary['performance_summary']['total_runtime_seconds']:.2f}s")
        print(f"Recommendations: {summary['recommendations_count']}")
        print()
        print(f"📁 Detailed results saved to: {tester.test_dir}")
        print(f"📋 See test_report.md for human-readable summary")

        # Determine exit code based on verdict
        if summary['test_verdict'].startswith('FAILED'):
            sys.exit(1)
        elif summary['test_verdict'].startswith('POOR'):
            sys.exit(2)
        else:
            sys.exit(0)

    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
