#!/bin/bash
# Example usage scripts for JIRA Ticket Similarity Matrix Generator

echo "=== JIRA Ticket Similarity Matrix Generator - Example Usage ==="
echo

# 1. Quick test with synthetic tickets
echo "1. Running quick test with synthetic tickets..."
python3 test_similarity_matrix.py --tickets 5 --model qwen3

echo
echo "2. Testing with real JIRA tickets (small subset)..."
# Create a small subset for testing
python3 -c "
import json
with open('enriched_tickets_clean.json', 'r') as f:
    tickets = json.load(f)
with open('sample_tickets.json', 'w') as f:
    json.dump(tickets[:10], f, indent=2)
print('Created sample_tickets.json with 10 tickets')
"

# Run similarity matrix on sample
python3 jira_similarity_matrix.py \
    --tickets sample_tickets.json \
    --output-json sample_similarity_matrix.json \
    --output-csv sample_similarity_matrix.csv \
    --cache-file sample_cache.json \
    --model-preset qwen3

echo
echo "3. Analyzing results..."
python3 test_similarity_matrix.py --analyze-only

echo
echo "4. Example: Running on full dataset with caching (commented out for safety)"
echo "# python3 jira_similarity_matrix.py \\"
echo "#     --tickets enriched_tickets_clean.json \\"
echo "#     --output-json full_similarity_matrix.json \\"
echo "#     --output-csv full_similarity_matrix.csv \\"
echo "#     --cache-file similarity_cache.json \\"
echo "#     --model-preset qwen3 \\"
echo "#     --temperature 0.1"

echo
echo "5. Example: Running without file changes consideration"
echo "# python3 jira_similarity_matrix.py \\"
echo "#     --no-include-files \\"
echo "#     --output-json desc_only_matrix.json"

echo
echo "6. Example: Using different model for faster processing"
echo "# python3 jira_similarity_matrix.py \\"
echo "#     --model-preset alma13 \\"
echo "#     --output-json fast_matrix.json"

echo
echo "=== Performance Estimates ==="
echo "For the full dataset (~500 tickets = 250,000 comparisons):"
echo "  - Estimated time with qwen3: ~8-12 hours"
echo "  - Estimated time with alma13: ~4-6 hours"
echo "  - Memory usage: ~2-4GB"
echo "  - Cache file size: ~50MB"
echo
echo "Recommendations:"
echo "  - Use caching (--cache-file) for large datasets"
echo "  - Start with smaller subsets to validate approach"
echo "  - Use alma13 model for faster processing if quality is acceptable"
echo "  - Monitor system resources during processing"

echo
echo "=== Output Files ==="
echo "Generated files:"
ls -la sample_* 2>/dev/null || echo "No sample files generated yet"

echo
echo "To clean up test files:"
echo "rm -f sample_tickets.json sample_similarity_matrix.json sample_similarity_matrix.csv sample_cache.json"
