# JIRA Similarity Matrix - Model Comparison Results

## Executive Summary

After comprehensive testing of qwen3 and gemma3 models for JIRA ticket similarity matrix generation, **GEMMA3 is recommended as the default model** due to superior reliability and JSON compliance.

## Test Configuration

- **Test Dataset**: 6 real JIRA tickets from enriched_tickets_clean.json
- **Total Comparisons**: 36 (6×6 matrix)
- **Test Date**: 2025-06-20
- **Evaluation Criteria**: Self-comparison consistency, processing speed, JSON compliance, validation metrics

## Detailed Results

### QWEN3 Performance
- **Status**: ❌ **FAILED**
- **Processing Speed**: ~3.7s per comparison (4x faster than gemma3)
- **Critical Issues**:
  - Failed to generate valid JSON responses
  - Ignored `/no_think` directive
  - Generated verbose explanations instead of structured output
  - 0/6 valid self-comparisons
  - Complete validation failure

**Example Failure Response:**
```
**Note:** The two tickets have the same title and description, but one has a note about a duplicate ticket. However, since the content is identical, the similarity score would be high. But since it's the same ticket, the score should be 1.0. But since the user says they are different tickets with same content, the score is 0.75...
```

### GEMMA3 Performance
- **Status**: ✅ **PASSED**
- **Processing Speed**: 17.65s per comparison
- **Validation Results**:
  - Self-comparison: ✅ PASS (6/6 scores ≥0.95)
  - Symmetry: ✅ PASS (0.0000 error rate)
  - Score range: ✅ PASS (all scores 0.0-1.0)
  - Completeness: ✅ PASS (36/36 comparisons)

**Self-Comparison Analysis:**
- Average score: 0.958
- Min score: 0.950
- Max score: 1.000
- Consistency (std dev): 0.020

**Non-Self Comparison Analysis:**
- Average score: 0.750
- Perfect symmetry (0.0000 error)
- No parsing failures

## Key Findings

### 1. JSON Compliance
- **GEMMA3**: Perfect adherence to JSON-only output format
- **QWEN3**: Consistently failed to follow JSON-only instructions

### 2. Prompt Following
- **GEMMA3**: Excellent compliance with `/no_think` directive
- **QWEN3**: Ignored instructions, generated verbose explanations

### 3. Self-Comparison Validation
- **GEMMA3**: 100% success rate (6/6 ≥0.95)
- **QWEN3**: 0% success rate due to parsing failures

### 4. Processing Speed
- **QWEN3**: ~3.7s per comparison
- **GEMMA3**: ~17.65s per comparison
- **Trade-off**: Reliability vs Speed - reliability is more critical

## Recommendations

### Primary Recommendation: GEMMA3 as Default
**Reasons:**
1. **100% reliability** vs 0% for qwen3
2. **Perfect JSON compliance** for structured output
3. **Excellent validation metrics** across all categories
4. **Consistent self-comparison scores** indicating good prompt understanding

### Speed Considerations
While gemma3 is ~4x slower than qwen3:
- **Quality over speed**: Reliability is paramount for analytical tasks
- **Caching mitigates speed**: Repeated comparisons are cached
- **Acceptable for batch processing**: ~50 hours for full dataset vs ~10 hours
- **No retry overhead**: Reliable first-time processing vs multiple retries

### When to Use Each Model
- **GEMMA3**: **Recommended for all production use**
  - Reliable similarity matrix generation
  - JSON-compliant responses
  - Excellent validation metrics
  
- **QWEN3**: **Not recommended**
  - Unreliable JSON parsing
  - Poor prompt following
  - Validation failures

## Implementation Changes Made

1. **Updated default model**: Changed from qwen3 to gemma3 in `jira_similarity_matrix.py`
2. **Removed alma13**: Eliminated inadequate model from all configurations
3. **Updated documentation**: Reflected new recommendations in README
4. **Test script updates**: Prioritized gemma3 in test ordering

## Performance Estimates (Full Dataset)

For ~500 tickets (250,000 comparisons):

| Model | Time Estimate | Reliability | Recommendation |
|-------|---------------|-------------|----------------|
| GEMMA3 | ~50 hours | 100% | ✅ **Use for production** |
| QWEN3 | ~10 hours | 0% | ❌ **Do not use** |

## Quality Validation Framework

The testing established a robust validation framework:

1. **Self-Comparison Check**: Scores ≥0.95 for identical tickets
2. **Symmetry Validation**: similarity(A,B) ≈ similarity(B,A)
3. **Score Range Validation**: All scores within [0.0, 1.0]
4. **Completeness Check**: Full N×N matrix generation
5. **JSON Compliance**: Structured output parsing

## Conclusion

**GEMMA3 is the clear choice** for JIRA ticket similarity matrix generation due to:
- Superior reliability and consistency
- Perfect JSON compliance
- Excellent validation metrics
- Predictable processing behavior

While slower than qwen3, the reliability advantage makes gemma3 the only viable option for production similarity matrix generation tasks.

## Next Steps

1. **Use gemma3 as default** for all similarity matrix generation
2. **Implement caching strategies** to mitigate speed concerns
3. **Consider batch processing** for large datasets
4. **Monitor performance** on full dataset to validate estimates
5. **Explore prompt optimizations** if needed while maintaining reliability
