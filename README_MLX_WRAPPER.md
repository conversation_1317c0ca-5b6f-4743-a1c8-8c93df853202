# MLX Model Wrapper

A reusable MLX model wrapper component that abstracts the MLX model invocation pattern with comprehensive logging capabilities.

## Overview

The MLX wrapper (`mlx_wrapper.py`) extracts the common MLX model interaction pattern used throughout the codebase and provides:

- **Abstracted MLX calling pattern**: Encapsulates sampler creation, logits processors setup, and generate() calls
- **Comprehensive logging**: Automatic logging of all LLM interactions with timestamps and metadata
- **Configurable parameters**: Support for temperature, repetition penalty, max tokens, etc.
- **Separation of concerns**: Domain-specific logic remains in calling modules
- **Drop-in replacement**: Easy migration from direct MLX calls

## Features

### 🔧 **MLX Integration**
- Supports all model presets from `shared_types.py` (gemma3, qwen3)
- Automatic model loading and configuration
- Follows established MLX patterns from existing codebase

### 📊 **Comprehensive Logging**
- **Request logging**: Prompt details, model parameters, timestamps
- **Response logging**: Generated text, token counts, processing times
- **Error logging**: Failed requests with error details and timing
- **Structured format**: JSON Lines format for easy parsing and analysis

### ⚙️ **Configurable Parameters**
- Temperature, top_p, max_tokens
- Repetition penalty and context size
- Verbose output control
- Custom log directory paths

### 🔄 **Reusable Design**
- Clean interface for different use cases
- Maintains compatibility with existing code
- Easy integration across multiple modules

## Installation

The MLX wrapper uses the same dependencies as the existing codebase:

```bash
pip install mlx-lm
```

## Usage

### Basic Usage

```python
from mlx_wrapper import MLXWrapper
from pathlib import Path

# Initialize wrapper
wrapper = MLXWrapper(
    model_preset="gemma3",  # or "qwen3"
    log_dir=Path("logs/mlx_interactions"),
    logging_enabled=True
)

# Generate text
response = wrapper.generate(
    prompt="Your prompt here",
    temperature=0.1,
    max_tokens=200
)
```

### Advanced Configuration

```python
# Custom parameters
response = wrapper.generate(
    prompt="Your prompt here",
    temperature=0.7,
    top_p=0.95,
    max_tokens=500,
    repetition_penalty=1.1,
    repetition_context_size=50,
    verbose=True
)
```

### Integration Example

Replace this pattern:
```python
# Old direct MLX usage
sampler = make_sampler(temp=0.1, top_p=0.9)
processors = make_logits_processors(repetition_penalty=1.05, repetition_context_size=256)

response = generate(
    model, tokenizer,
    prompt=prompt,
    max_tokens=200,
    sampler=sampler,
    logits_processors=processors,
    verbose=False
)
```

With this:
```python
# New wrapper usage
response = mlx_wrapper.generate(
    prompt=prompt,
    temperature=0.1,
    top_p=0.9,
    max_tokens=200,
    repetition_penalty=1.05,
    repetition_context_size=256,
    verbose=False
)
```

## Logging

### Log Structure

The wrapper creates structured logs in JSON Lines format:

**Request logs** (`mlx_requests.jsonl`):
```json
{
  "timestamp": 1703123456.789,
  "type": "request",
  "request_id": "uuid-string",
  "prompt_length": 150,
  "prompt_preview": "You are an expert...",
  "model_preset": "gemma3",
  "temperature": 0.1,
  "top_p": 0.9,
  "max_tokens": 200,
  "repetition_penalty": 1.05,
  "repetition_context_size": 256
}
```

**Response logs** (`mlx_responses.jsonl`):
```json
{
  "timestamp": 1703123457.123,
  "type": "response",
  "request_id": "uuid-string",
  "response_length": 85,
  "response_preview": "{\n  \"similarity_score\": 0.75...",
  "token_count": 23,
  "processing_time": 2.34,
  "success": true,
  "error_message": null
}
```

### Log Analysis

Use the logs for:
- **Performance monitoring**: Track processing times and token counts
- **Debugging**: Correlate requests and responses using request_id
- **Usage analytics**: Analyze prompt patterns and model performance
- **Error tracking**: Monitor failed requests and error patterns

## Integration with Existing Code

### JIRA Similarity Matrix

The wrapper has been integrated into `jira_similarity_matrix.py`:

```python
# Before
class SimilarityMatrixGenerator:
    def __init__(self, config):
        self.model, self.tokenizer = load(model_path)
    
    def _generate_similarity_score(self, ticket_a, ticket_b):
        # Direct MLX calls...

# After  
class SimilarityMatrixGenerator:
    def __init__(self, config):
        self.mlx_wrapper = MLXWrapper(
            model_preset=config.model_preset,
            log_dir=Path("logs/mlx_similarity"),
            logging_enabled=True
        )
    
    def _generate_similarity_score(self, ticket_a, ticket_b):
        response = self.mlx_wrapper.generate(...)
```

### Other Modules

The wrapper can be easily integrated into other modules like `idea_refiner.py`:

```python
# Replace direct MLX usage in IntelligentLLMRefiner
class IntelligentLLMRefiner:
    def __init__(self, model_preset="gemma3"):
        self.mlx_wrapper = MLXWrapper(
            model_preset=model_preset,
            log_dir=Path("logs/mlx_refinement"),
            logging_enabled=True
        )
    
    def _generate_llm_response(self, prompt):
        return self.mlx_wrapper.generate(prompt=prompt, ...)
```

## Testing

Run the test suite to verify functionality:

```bash
python test_mlx_wrapper.py
```

The test suite includes:
- Basic generation functionality
- Similarity comparison pattern (used by jira_similarity_matrix.py)
- Logging verification

## Configuration

### Model Presets

The wrapper uses model presets from `shared_types.py`:

- **gemma3**: Recommended default (8K context, reliable)
- **qwen3**: Alternative option (32K context, faster but less reliable JSON parsing)

### Log Directory Structure

```
logs/
├── mlx_interactions/
│   ├── mlx_requests.jsonl
│   └── mlx_responses.jsonl
├── mlx_similarity/
│   ├── mlx_requests.jsonl
│   └── mlx_responses.jsonl
└── mlx_refinement/
    ├── mlx_requests.jsonl
    └── mlx_responses.jsonl
```

## Best Practices

1. **Use appropriate model presets**: gemma3 for reliability, qwen3 for speed
2. **Configure log directories**: Use descriptive paths for different use cases
3. **Monitor logs**: Regularly check logs for performance and error patterns
4. **Handle errors gracefully**: The wrapper preserves original error handling patterns
5. **Use consistent parameters**: Follow established patterns for temperature, repetition penalty, etc.

## Migration Guide

To migrate existing MLX usage to the wrapper:

1. **Replace imports**: Remove direct MLX imports, add wrapper import
2. **Initialize wrapper**: Create MLXWrapper instance in constructor
3. **Replace generate calls**: Use wrapper.generate() instead of direct calls
4. **Configure logging**: Set appropriate log directory for your use case
5. **Test thoroughly**: Verify functionality with existing prompts and patterns

## Troubleshooting

### Common Issues

1. **Model not found**: Check model paths in `shared_types.py`
2. **Permission errors**: Ensure log directory is writable
3. **Memory issues**: Use gemma3 model for lower memory usage
4. **JSON parsing**: The wrapper preserves original response format

### Debug Mode

Enable debug logging to see detailed wrapper operations:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

wrapper = MLXWrapper(...)
```

## Future Enhancements

Potential improvements:
- Token counting integration with actual tokenizer
- Response caching for identical prompts
- Batch processing support
- Performance metrics aggregation
- Integration with monitoring systems
