#!/usr/bin/env python3

import json
import logging
import time
import uuid
from dataclasses import dataclass, field
from functools import lru_cache
from pathlib import Path
from typing import Dict, Any, Optional

from mlx_lm import load, generate
from mlx_lm.sample_utils import make_sampler, make_logits_processors

from shared_types import MLX_PRESETS


@dataclass
class MLXRequest:
    request_id: str
    prompt: str
    prompt_length: int
    model_preset: str
    temperature: float
    top_p: float
    max_tokens: int
    repetition_penalty: float
    repetition_context_size: int
    timestamp: float = field(default_factory=time.time)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "request_id": self.request_id,
            "prompt_length": self.prompt_length,
            "prompt": self.prompt,
            "model_preset": self.model_preset,
            "temperature": self.temperature,
            "top_p": self.top_p,
            "max_tokens": self.max_tokens,
            "repetition_penalty": self.repetition_penalty,
            "repetition_context_size": self.repetition_context_size,
            "timestamp": self.timestamp
        }


@dataclass
class MLXResponse:
    request_id: str
    response_text: str
    response_length: int
    token_count: int
    processing_time: float
    success: bool
    error_message: Optional[str] = None
    timestamp: float = field(default_factory=time.time)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "request_id": self.request_id,
            "response_length": self.response_length,
            "response_text": self.response_text,
            "token_count": self.token_count,
            "processing_time": self.processing_time,
            "success": self.success,
            "error_message": self.error_message,
            "timestamp": self.timestamp
        }


class MLXLogger:

    def __init__(self, log_dir: Optional[Path] = None, enabled: bool = True):
        if not enabled:
            return

        self.enabled = enabled
        self.logger = logging.getLogger(f"{__name__}.MLXLogger")

        self.log_dir = log_dir or Path("logs/mlx_interactions")
        self.log_dir.mkdir(parents=True, exist_ok=True)

        self._setup_file_handlers()
        self.logger.info(f"MLX logging initialized - logs will be written to {self.log_dir}")

    def _setup_file_handlers(self):
        requests_handler = logging.FileHandler(self.log_dir / "mlx_requests.jsonl")
        requests_handler.setLevel(logging.INFO)
        requests_formatter = logging.Formatter('%(message)s')
        requests_handler.setFormatter(requests_formatter)

        responses_handler = logging.FileHandler(self.log_dir / "mlx_responses.jsonl")
        responses_handler.setLevel(logging.INFO)
        responses_formatter = logging.Formatter('%(message)s')
        responses_handler.setFormatter(responses_formatter)

        self.requests_logger = logging.getLogger(f"{__name__}.requests")
        self.requests_logger.addHandler(requests_handler)
        self.requests_logger.setLevel(logging.INFO)
        self.requests_logger.propagate = False

        self.responses_logger = logging.getLogger(f"{__name__}.responses")
        self.responses_logger.addHandler(responses_handler)
        self.responses_logger.setLevel(logging.INFO)
        self.responses_logger.propagate = False

    def log_request(self, request: MLXRequest):
        if not self.enabled:
            return

        try:
            self.requests_logger.info(json.dumps({
                "timestamp": request.timestamp,
                "type": "request",
                **request.to_dict()
            }))
        except Exception as e:
            self.logger.error(f"Failed to log request: {e}")

    def log_response(self, response: MLXResponse):
        if not self.enabled:
            return

        try:
            log_entry = {
                "timestamp": response.timestamp,
                "type": "response",
                **response.to_dict()
            }
            self.responses_logger.info(json.dumps(log_entry))
            self.logger.debug(f"Logged response {response.request_id}")
        except Exception as e:
            self.logger.error(f"Failed to log response: {e}")


class MLXWrapper:

    def __init__(self, model_preset: str = "gemma3", log_dir: Optional[Path] = None,
                 logging_enabled: bool = False):
        self.logger = logging.getLogger(__name__)

        self.mlx_logger = MLXLogger(log_dir, logging_enabled)

        if model_preset not in MLX_PRESETS:
            raise ValueError(f"Unknown model preset: {model_preset}. Available: {list(MLX_PRESETS.keys())}")

        self.config = MLX_PRESETS[model_preset]
        self._load_model()

        self.logger.info(f"MLX wrapper initialized with {model_preset} model")

    def _load_model(self):
        try:
            self.logger.info(f"Loading model from {self.config.model_path}")
            self.model, self.tokenizer = load(self.config.model_path)
            self.logger.info(f"Model loaded successfully (context: {self.config.max_context} tokens)")
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            raise

    def generate(self, prompt: str, temperature: Optional[float] = None,
                 top_p: float = 0.9, max_tokens: Optional[int] = None,
                 repetition_penalty: Optional[float] = None,
                 repetition_context_size: Optional[int] = None,
                 verbose: bool = False) -> str:
        temperature = temperature if temperature is not None else self.config.temperature
        max_tokens = max_tokens if max_tokens is not None else self.config.max_tokens
        repetition_penalty = repetition_penalty if repetition_penalty is not None else self.config.repetition_penalty
        repetition_context_size = repetition_context_size if repetition_context_size is not None else self.config.repetition_context_size

        request_id = str(uuid.uuid4())

        request = MLXRequest(
            request_id=request_id,
            prompt=prompt,
            prompt_length=self.count_tokens(prompt),
            model_preset=self.config.model_preset,
            temperature=temperature,
            top_p=top_p,
            max_tokens=max_tokens,
            repetition_penalty=repetition_penalty,
            repetition_context_size=repetition_context_size
        )

        # Log the request
        self.mlx_logger.log_request(request)

        start_time = time.time()

        try:
            sampler = make_sampler(temp=temperature, top_p=top_p)
            processors = make_logits_processors(
                repetition_penalty=repetition_penalty,
                repetition_context_size=repetition_context_size
            )

            response_text = generate(
                self.model,
                self.tokenizer,
                prompt=prompt,
                max_tokens=max_tokens,
                sampler=sampler,
                logits_processors=processors,
                verbose=verbose
            )

            processing_time = time.time() - start_time

            self.mlx_logger.log_response(MLXResponse(
                request_id=request_id,
                response_text=response_text,
                response_length=self.count_tokens(response_text),
                token_count=(self.count_tokens(response_text)),
                processing_time=processing_time,
                success=True
            ))
            return response_text

        except Exception as e:
            processing_time = time.time() - start_time

            self.mlx_logger.log_response(MLXResponse(
                request_id=request_id,
                response_text="",
                response_length=0,
                token_count=0,
                processing_time=processing_time,
                success=False,
                error_message=str(e)
            ))

            self.logger.error(f"MLX generation failed after {processing_time:.2f}s: {e}")
            raise

    @lru_cache(maxsize=1000)
    def count_tokens(self, text: str) -> int:
        try:
            return len(self.tokenizer.encode(text))
        except Exception:
            return len(text) // 4  # Rough estimate
