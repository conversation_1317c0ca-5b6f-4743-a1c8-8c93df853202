{"timestamp": 1750510472.004169, "type": "request", "request_id": "18555892-d706-424d-9c85-c06ca2a83b93", "prompt_length": 549, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\n**Ticket B (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750510495.54762, "type": "request", "request_id": "b3c02d3c-4798-4882-b0ec-7373706e2ef2", "prompt_length": 755, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\n**Ticket B (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750510523.2160242, "type": "request", "request_id": "540a27c4-7893-4d80-af19-aa222aeed1c5", "prompt_length": 436, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\n**Ticket B (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750510547.092454, "type": "request", "request_id": "c2996a32-2bec-4955-8cdb-a9712164b0b0", "prompt_length": 755, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750510578.0870988, "type": "request", "request_id": "165f5851-9b4d-497f-91cb-6d0bdf8f8e10", "prompt_length": 961, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750510616.4332721, "type": "request", "request_id": "f738c175-bd2f-49ad-b84f-99388177adaa", "prompt_length": 642, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750510659.596351, "type": "request", "request_id": "4c8ad9f7-65a8-4d12-9fe3-1d54047217f6", "prompt_length": 436, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750510691.627376, "type": "request", "request_id": "b0ddeec8-a7e8-47f4-a787-455ab9394713", "prompt_length": 642, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750510741.799927, "type": "request", "request_id": "7d13cb72-0548-4edc-82ee-308af1b5136f", "prompt_length": 323, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750510846.369092, "type": "request", "request_id": "3b7b243d-ca19-482b-8f7f-a0ed532a7d3f", "prompt_length": 630, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\n**Ticket B (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750510883.023746, "type": "request", "request_id": "9ea13829-f574-4caa-90d5-5553a13cb036", "prompt_length": 836, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\n**Ticket B (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750510931.4950662, "type": "request", "request_id": "5b797f5b-57de-4409-ae88-e3aea6184bb1", "prompt_length": 517, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\n**Ticket B (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750510971.123897, "type": "request", "request_id": "606324d1-e2e3-460c-9230-cb8239bd2e95", "prompt_length": 575, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\n**Ticket B (MARS-2811):**\nTitle: CARBS Terminal Migration on 05/12\nDescription: *Business Value:*  To Migrate F2F terminals for VISA & Mastercard merchants on CARBS\n\n*Acceptance Criteria:*\n\nF2F terminals with required details to migrate are provided in CSV format as required. see attached\n\n[^F2F_05122024.csv]\n\n...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511010.563864, "type": "request", "request_id": "06f5c83c-b2b4-4bb7-91b0-eff3d671d149", "prompt_length": 2245, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\n**Ticket B (MARS-2810):**\nTitle: enable cross-region access from AG txn svc to auth-amex\nDescription: [Diagram of current proposed solution|https://lucid.app/lucidchart/2989ef11-5d82-4780-bd19-e9f943a1fc5a/edit?invitationId=inv_77dd7e8b-eec0-4778-ac08-9f35da367c81&page=iN~w_5tMaDZC#]\n\n\n\nThe proposed solution for removing local cluster URLs is as follows:\n\n* Set up static IP for ALBs \n** This has been completed already in staging [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/frankfurt/gke/terragrunt.hcl#L53] and [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/london/gke/terragrunt.hcl#L54], still needs finishing in prod\n* Generate internal ALBs with static IPs\n** this is done [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ld-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L94] and [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ff-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L43]\n** Currently this is not working as expected - the internal ALBs need to be created as global ALBs, but there\u2019s no way to do this when creating them with k8s annotations, so this approach _may_ need to be rethought. [~accountid:5de502972fd6260cf27cdc0d] has requested guidance from David (04-12-24) - this ticket will be updated with any proposed changes. If David does not get back before Harry is on leave please whoever picks up this ticket follow up with David.\n* Update host alias in MARSGCP transaction services in AG + adjust endpoint URLs\n** This has been started for staging [here|https://github.com/dojo-engineering/auth-gateway/pull/4294] - until the ALB issue is resolved it cannot be properly tested so the PR remains in draft and unmerged.\n** *Note:* it was decided to retain the local cluster URLs for communication with services in the same cluster to keep things simple (e.g. AG MARSGCPLondon transaction service \u2192 auth-amex London should use a local cluster URL)\n*** that means we have to update MARSGCPLondon instance deployed in FF and MARSGCPFrankfurt instance deployed in London (2 configs) with IP addresses of above ALBs\n* Testing strategy\n** The current e2e testing strategy for this change is to use the [AGs end-to-end test tool|https://github.com/dojo-engineering/auth-gateway/tree/ce5410eb53950b19a70a44e5fbc8967ff3283d49/end-to-end-tests] to send requests to MARSGCPLondon in staging using the following command:\n*** {{./e2e payment staging-ld-purchase --processor=MARSGCPLondon --card-scheme=amex}}\n** [A GCP connectivity test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] ({{ldn-ff-peered-networking-test}}) has also been setup and may provide a faster feedback loop \n\n\n\n\n\nSoooo, I think I've found the issue, it basically boils down to the ILBs not being global, and there's no way for us to set that from k8s that I can tell. This is just something we're not going to be able to fix with the way we've currently got the ALBs setup\n\nThere's an [outstanding issue|https://issuetracker.google.com/issues/272577313] and something in the [k8s/ingress-gce|https://github.com/kubernetes/ingress-gce/issues/1887] repo suggesting that what we're trying to do is just not [http://possible.To|http://possible.To|smart-link]  speed up debugging I added this [ld-ff-peered-networking-test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] which is what pointed me in the direction in the first place\n\n* I've tried adding the {{networking.gke.io/internal-load-balancer-allow-global-access: \"true\"}} annotation to the service/ingress but it looks like this is just for l4 lbs - [https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|smart-link] \n* I've tried adding a [GCPGatewayPolicy|https://cloud.google.com/kubernetes-engine/docs/how-to/configure-gateway-resources] but these only apply to {{gateway.networking.k8s.io}} - not our\u00a0 Istio gateways\n* I tried manually creating a LB frontend with global access set to true, and that actually worked, but probably not something we'll want to do in prod (ss1)\n* I tried updating it with a gcloud command, that failed:\n\n{noformat}payments-infrastructure \ue7f0 (dojo-payments-nonprod) \uf418 test-global-lb-access \u276f gcloud beta compute forwarding-rules update k8s2-fs-8d36fjog-istio-ing-istio-ingress-v2-stg-auth-a-qy272bvf --region europe-west3 --allow-global-access                          \nERROR: (gcloud.beta.compute.forwarding-rules.update) Could not fetch resource:\n - Invalid value for field 'resource.allowGlobalAccess': 'true'. Updating the allow-global-access flag is only supported for regional INTERNAL forwarding rules with backend service/target instance.{noformat}\n\nin terms of potential fixes...\n\n* We could setup the global internal lb manually using [terraform|https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule#example-usage---forwarding-rule-global-internallb]\n* We could make the lb external? Although that seems like it shouldn't be necessary when we have cluster networking peering setup?\n* Switch to an l4 LB and use the annotation above?\n\n\n\n*Business Value:*\n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *KINDA*\n** F2F AG will do the release since it changes their config - hence the notification will happen and AG will be in context\n* Does it affect resources shared with Clearing? *NO*\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511103.824422, "type": "request", "request_id": "920d5a47-8693-41d4-ab12-60ecf7cb20a6", "prompt_length": 836, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511158.2134469, "type": "request", "request_id": "eba34fb6-c1d0-4e70-bd40-77c04c3b4773", "prompt_length": 1042, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511213.129177, "type": "request", "request_id": "05569943-412a-405b-91c8-4f763d466d1a", "prompt_length": 723, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511265.628254, "type": "request", "request_id": "bd78d8c1-a0cf-4577-9f96-4109d7be2d7a", "prompt_length": 781, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2811):**\nTitle: CARBS Terminal Migration on 05/12\nDescription: *Business Value:*  To Migrate F2F terminals for VISA & Mastercard merchants on CARBS\n\n*Acceptance Criteria:*\n\nF2F terminals with required details to migrate are provided in CSV format as required. see attached\n\n[^F2F_05122024.csv]\n\n...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511314.679132, "type": "request", "request_id": "fcca8a82-ca7b-4c50-9473-09b98b96ea0a", "prompt_length": 2451, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2810):**\nTitle: enable cross-region access from AG txn svc to auth-amex\nDescription: [Diagram of current proposed solution|https://lucid.app/lucidchart/2989ef11-5d82-4780-bd19-e9f943a1fc5a/edit?invitationId=inv_77dd7e8b-eec0-4778-ac08-9f35da367c81&page=iN~w_5tMaDZC#]\n\n\n\nThe proposed solution for removing local cluster URLs is as follows:\n\n* Set up static IP for ALBs \n** This has been completed already in staging [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/frankfurt/gke/terragrunt.hcl#L53] and [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/london/gke/terragrunt.hcl#L54], still needs finishing in prod\n* Generate internal ALBs with static IPs\n** this is done [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ld-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L94] and [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ff-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L43]\n** Currently this is not working as expected - the internal ALBs need to be created as global ALBs, but there\u2019s no way to do this when creating them with k8s annotations, so this approach _may_ need to be rethought. [~accountid:5de502972fd6260cf27cdc0d] has requested guidance from David (04-12-24) - this ticket will be updated with any proposed changes. If David does not get back before Harry is on leave please whoever picks up this ticket follow up with David.\n* Update host alias in MARSGCP transaction services in AG + adjust endpoint URLs\n** This has been started for staging [here|https://github.com/dojo-engineering/auth-gateway/pull/4294] - until the ALB issue is resolved it cannot be properly tested so the PR remains in draft and unmerged.\n** *Note:* it was decided to retain the local cluster URLs for communication with services in the same cluster to keep things simple (e.g. AG MARSGCPLondon transaction service \u2192 auth-amex London should use a local cluster URL)\n*** that means we have to update MARSGCPLondon instance deployed in FF and MARSGCPFrankfurt instance deployed in London (2 configs) with IP addresses of above ALBs\n* Testing strategy\n** The current e2e testing strategy for this change is to use the [AGs end-to-end test tool|https://github.com/dojo-engineering/auth-gateway/tree/ce5410eb53950b19a70a44e5fbc8967ff3283d49/end-to-end-tests] to send requests to MARSGCPLondon in staging using the following command:\n*** {{./e2e payment staging-ld-purchase --processor=MARSGCPLondon --card-scheme=amex}}\n** [A GCP connectivity test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] ({{ldn-ff-peered-networking-test}}) has also been setup and may provide a faster feedback loop \n\n\n\n\n\nSoooo, I think I've found the issue, it basically boils down to the ILBs not being global, and there's no way for us to set that from k8s that I can tell. This is just something we're not going to be able to fix with the way we've currently got the ALBs setup\n\nThere's an [outstanding issue|https://issuetracker.google.com/issues/272577313] and something in the [k8s/ingress-gce|https://github.com/kubernetes/ingress-gce/issues/1887] repo suggesting that what we're trying to do is just not [http://possible.To|http://possible.To|smart-link]  speed up debugging I added this [ld-ff-peered-networking-test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] which is what pointed me in the direction in the first place\n\n* I've tried adding the {{networking.gke.io/internal-load-balancer-allow-global-access: \"true\"}} annotation to the service/ingress but it looks like this is just for l4 lbs - [https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|smart-link] \n* I've tried adding a [GCPGatewayPolicy|https://cloud.google.com/kubernetes-engine/docs/how-to/configure-gateway-resources] but these only apply to {{gateway.networking.k8s.io}} - not our\u00a0 Istio gateways\n* I tried manually creating a LB frontend with global access set to true, and that actually worked, but probably not something we'll want to do in prod (ss1)\n* I tried updating it with a gcloud command, that failed:\n\n{noformat}payments-infrastructure \ue7f0 (dojo-payments-nonprod) \uf418 test-global-lb-access \u276f gcloud beta compute forwarding-rules update k8s2-fs-8d36fjog-istio-ing-istio-ingress-v2-stg-auth-a-qy272bvf --region europe-west3 --allow-global-access                          \nERROR: (gcloud.beta.compute.forwarding-rules.update) Could not fetch resource:\n - Invalid value for field 'resource.allowGlobalAccess': 'true'. Updating the allow-global-access flag is only supported for regional INTERNAL forwarding rules with backend service/target instance.{noformat}\n\nin terms of potential fixes...\n\n* We could setup the global internal lb manually using [terraform|https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule#example-usage---forwarding-rule-global-internallb]\n* We could make the lb external? Although that seems like it shouldn't be necessary when we have cluster networking peering setup?\n* Switch to an l4 LB and use the annotation above?\n\n\n\n*Business Value:*\n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *KINDA*\n** F2F AG will do the release since it changes their config - hence the notification will happen and AG will be in context\n* Does it affect resources shared with Clearing? *NO*\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511436.318712, "type": "request", "request_id": "5e81abb5-714c-4dbb-8f34-1f5a3dd3f0e6", "prompt_length": 2451, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2810):**\nTitle: enable cross-region access from AG txn svc to auth-amex\nDescription: [Diagram of current proposed solution|https://lucid.app/lucidchart/2989ef11-5d82-4780-bd19-e9f943a1fc5a/edit?invitationId=inv_77dd7e8b-eec0-4778-ac08-9f35da367c81&page=iN~w_5tMaDZC#]\n\n\n\nThe proposed solution for removing local cluster URLs is as follows:\n\n* Set up static IP for ALBs \n** This has been completed already in staging [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/frankfurt/gke/terragrunt.hcl#L53] and [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/london/gke/terragrunt.hcl#L54], still needs finishing in prod\n* Generate internal ALBs with static IPs\n** this is done [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ld-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L94] and [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ff-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L43]\n** Currently this is not working as expected - the internal ALBs need to be created as global ALBs, but there\u2019s no way to do this when creating them with k8s annotations, so this approach _may_ need to be rethought. [~accountid:5de502972fd6260cf27cdc0d] has requested guidance from David (04-12-24) - this ticket will be updated with any proposed changes. If David does not get back before Harry is on leave please whoever picks up this ticket follow up with David.\n* Update host alias in MARSGCP transaction services in AG + adjust endpoint URLs\n** This has been started for staging [here|https://github.com/dojo-engineering/auth-gateway/pull/4294] - until the ALB issue is resolved it cannot be properly tested so the PR remains in draft and unmerged.\n** *Note:* it was decided to retain the local cluster URLs for communication with services in the same cluster to keep things simple (e.g. AG MARSGCPLondon transaction service \u2192 auth-amex London should use a local cluster URL)\n*** that means we have to update MARSGCPLondon instance deployed in FF and MARSGCPFrankfurt instance deployed in London (2 configs) with IP addresses of above ALBs\n* Testing strategy\n** The current e2e testing strategy for this change is to use the [AGs end-to-end test tool|https://github.com/dojo-engineering/auth-gateway/tree/ce5410eb53950b19a70a44e5fbc8967ff3283d49/end-to-end-tests] to send requests to MARSGCPLondon in staging using the following command:\n*** {{./e2e payment staging-ld-purchase --processor=MARSGCPLondon --card-scheme=amex}}\n** [A GCP connectivity test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] ({{ldn-ff-peered-networking-test}}) has also been setup and may provide a faster feedback loop \n\n\n\n\n\nSoooo, I think I've found the issue, it basically boils down to the ILBs not being global, and there's no way for us to set that from k8s that I can tell. This is just something we're not going to be able to fix with the way we've currently got the ALBs setup\n\nThere's an [outstanding issue|https://issuetracker.google.com/issues/272577313] and something in the [k8s/ingress-gce|https://github.com/kubernetes/ingress-gce/issues/1887] repo suggesting that what we're trying to do is just not [http://possible.To|http://possible.To|smart-link]  speed up debugging I added this [ld-ff-peered-networking-test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] which is what pointed me in the direction in the first place\n\n* I've tried adding the {{networking.gke.io/internal-load-balancer-allow-global-access: \"true\"}} annotation to the service/ingress but it looks like this is just for l4 lbs - [https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|smart-link] \n* I've tried adding a [GCPGatewayPolicy|https://cloud.google.com/kubernetes-engine/docs/how-to/configure-gateway-resources] but these only apply to {{gateway.networking.k8s.io}} - not our\u00a0 Istio gateways\n* I tried manually creating a LB frontend with global access set to true, and that actually worked, but probably not something we'll want to do in prod (ss1)\n* I tried updating it with a gcloud command, that failed:\n\n{noformat}payments-infrastructure \ue7f0 (dojo-payments-nonprod) \uf418 test-global-lb-access \u276f gcloud beta compute forwarding-rules update k8s2-fs-8d36fjog-istio-ing-istio-ingress-v2-stg-auth-a-qy272bvf --region europe-west3 --allow-global-access                          \nERROR: (gcloud.beta.compute.forwarding-rules.update) Could not fetch resource:\n - Invalid value for field 'resource.allowGlobalAccess': 'true'. Updating the allow-global-access flag is only supported for regional INTERNAL forwarding rules with backend service/target instance.{noformat}\n\nin terms of potential fixes...\n\n* We could setup the global internal lb manually using [terraform|https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule#example-usage---forwarding-rule-global-internallb]\n* We could make the lb external? Although that seems like it shouldn't be necessary when we have cluster networking peering setup?\n* Switch to an l4 LB and use the annotation above?\n\n\n\n*Business Value:*\n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *KINDA*\n** F2F AG will do the release since it changes their config - hence the notification will happen and AG will be in context\n* Does it affect resources shared with Clearing? *NO*\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511557.032382, "type": "request", "request_id": "f6f95fe5-3372-49d5-8961-93dbb826d265", "prompt_length": 517, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511616.440642, "type": "request", "request_id": "900727e9-a15c-4cbb-bc47-bdcd348e5494", "prompt_length": 723, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511676.147011, "type": "request", "request_id": "903864b3-370e-4d86-9150-76ad126e70fa", "prompt_length": 404, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511719.5078158, "type": "request", "request_id": "1a6f0a97-43d9-4907-a949-75ae664b41fb", "prompt_length": 462, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2811):**\nTitle: CARBS Terminal Migration on 05/12\nDescription: *Business Value:*  To Migrate F2F terminals for VISA & Mastercard merchants on CARBS\n\n*Acceptance Criteria:*\n\nF2F terminals with required details to migrate are provided in CSV format as required. see attached\n\n[^F2F_05122024.csv]\n\n...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511773.842431, "type": "request", "request_id": "5efbf671-6e1d-4ec0-b779-00ae79854e93", "prompt_length": 2132, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2810):**\nTitle: enable cross-region access from AG txn svc to auth-amex\nDescription: [Diagram of current proposed solution|https://lucid.app/lucidchart/2989ef11-5d82-4780-bd19-e9f943a1fc5a/edit?invitationId=inv_77dd7e8b-eec0-4778-ac08-9f35da367c81&page=iN~w_5tMaDZC#]\n\n\n\nThe proposed solution for removing local cluster URLs is as follows:\n\n* Set up static IP for ALBs \n** This has been completed already in staging [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/frankfurt/gke/terragrunt.hcl#L53] and [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/london/gke/terragrunt.hcl#L54], still needs finishing in prod\n* Generate internal ALBs with static IPs\n** this is done [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ld-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L94] and [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ff-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L43]\n** Currently this is not working as expected - the internal ALBs need to be created as global ALBs, but there\u2019s no way to do this when creating them with k8s annotations, so this approach _may_ need to be rethought. [~accountid:5de502972fd6260cf27cdc0d] has requested guidance from David (04-12-24) - this ticket will be updated with any proposed changes. If David does not get back before Harry is on leave please whoever picks up this ticket follow up with David.\n* Update host alias in MARSGCP transaction services in AG + adjust endpoint URLs\n** This has been started for staging [here|https://github.com/dojo-engineering/auth-gateway/pull/4294] - until the ALB issue is resolved it cannot be properly tested so the PR remains in draft and unmerged.\n** *Note:* it was decided to retain the local cluster URLs for communication with services in the same cluster to keep things simple (e.g. AG MARSGCPLondon transaction service \u2192 auth-amex London should use a local cluster URL)\n*** that means we have to update MARSGCPLondon instance deployed in FF and MARSGCPFrankfurt instance deployed in London (2 configs) with IP addresses of above ALBs\n* Testing strategy\n** The current e2e testing strategy for this change is to use the [AGs end-to-end test tool|https://github.com/dojo-engineering/auth-gateway/tree/ce5410eb53950b19a70a44e5fbc8967ff3283d49/end-to-end-tests] to send requests to MARSGCPLondon in staging using the following command:\n*** {{./e2e payment staging-ld-purchase --processor=MARSGCPLondon --card-scheme=amex}}\n** [A GCP connectivity test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] ({{ldn-ff-peered-networking-test}}) has also been setup and may provide a faster feedback loop \n\n\n\n\n\nSoooo, I think I've found the issue, it basically boils down to the ILBs not being global, and there's no way for us to set that from k8s that I can tell. This is just something we're not going to be able to fix with the way we've currently got the ALBs setup\n\nThere's an [outstanding issue|https://issuetracker.google.com/issues/272577313] and something in the [k8s/ingress-gce|https://github.com/kubernetes/ingress-gce/issues/1887] repo suggesting that what we're trying to do is just not [http://possible.To|http://possible.To|smart-link]  speed up debugging I added this [ld-ff-peered-networking-test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] which is what pointed me in the direction in the first place\n\n* I've tried adding the {{networking.gke.io/internal-load-balancer-allow-global-access: \"true\"}} annotation to the service/ingress but it looks like this is just for l4 lbs - [https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|smart-link] \n* I've tried adding a [GCPGatewayPolicy|https://cloud.google.com/kubernetes-engine/docs/how-to/configure-gateway-resources] but these only apply to {{gateway.networking.k8s.io}} - not our\u00a0 Istio gateways\n* I tried manually creating a LB frontend with global access set to true, and that actually worked, but probably not something we'll want to do in prod (ss1)\n* I tried updating it with a gcloud command, that failed:\n\n{noformat}payments-infrastructure \ue7f0 (dojo-payments-nonprod) \uf418 test-global-lb-access \u276f gcloud beta compute forwarding-rules update k8s2-fs-8d36fjog-istio-ing-istio-ingress-v2-stg-auth-a-qy272bvf --region europe-west3 --allow-global-access                          \nERROR: (gcloud.beta.compute.forwarding-rules.update) Could not fetch resource:\n - Invalid value for field 'resource.allowGlobalAccess': 'true'. Updating the allow-global-access flag is only supported for regional INTERNAL forwarding rules with backend service/target instance.{noformat}\n\nin terms of potential fixes...\n\n* We could setup the global internal lb manually using [terraform|https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule#example-usage---forwarding-rule-global-internallb]\n* We could make the lb external? Although that seems like it shouldn't be necessary when we have cluster networking peering setup?\n* Switch to an l4 LB and use the annotation above?\n\n\n\n*Business Value:*\n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *KINDA*\n** F2F AG will do the release since it changes their config - hence the notification will happen and AG will be in context\n* Does it affect resources shared with Clearing? *NO*\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511879.901772, "type": "request", "request_id": "69189fb9-659f-40a7-9610-71c4e487c1d9", "prompt_length": 575, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2811):**\nTitle: CARBS Terminal Migration on 05/12\nDescription: *Business Value:*  To Migrate F2F terminals for VISA & Mastercard merchants on CARBS\n\n*Acceptance Criteria:*\n\nF2F terminals with required details to migrate are provided in CSV format as required. see attached\n\n[^F2F_05122024.csv]\n\n...\n\n**Ticket B (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511921.2443142, "type": "request", "request_id": "aaa45833-1e28-45f0-91a9-21dbdf62d893", "prompt_length": 781, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2811):**\nTitle: CARBS Terminal Migration on 05/12\nDescription: *Business Value:*  To Migrate F2F terminals for VISA & Mastercard merchants on CARBS\n\n*Acceptance Criteria:*\n\nF2F terminals with required details to migrate are provided in CSV format as required. see attached\n\n[^F2F_05122024.csv]\n\n...\n\n**Ticket B (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750511971.597833, "type": "request", "request_id": "2208652d-ce51-490c-a93d-53ff12ffcd63", "prompt_length": 462, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2811):**\nTitle: CARBS Terminal Migration on 05/12\nDescription: *Business Value:*  To Migrate F2F terminals for VISA & Mastercard merchants on CARBS\n\n*Acceptance Criteria:*\n\nF2F terminals with required details to migrate are provided in CSV format as required. see attached\n\n[^F2F_05122024.csv]\n\n...\n\n**Ticket B (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750512005.993325, "type": "request", "request_id": "e01dcb42-0981-4c43-87f4-1382924df9b6", "prompt_length": 520, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2811):**\nTitle: CARBS Terminal Migration on 05/12\nDescription: *Business Value:*  To Migrate F2F terminals for VISA & Mastercard merchants on CARBS\n\n*Acceptance Criteria:*\n\nF2F terminals with required details to migrate are provided in CSV format as required. see attached\n\n[^F2F_05122024.csv]\n\n...\n\n**Ticket B (MARS-2811):**\nTitle: CARBS Terminal Migration on 05/12\nDescription: *Business Value:*  To Migrate F2F terminals for VISA & Mastercard merchants on CARBS\n\n*Acceptance Criteria:*\n\nF2F terminals with required details to migrate are provided in CSV format as required. see attached\n\n[^F2F_05122024.csv]\n\n...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750512037.802331, "type": "request", "request_id": "12fb0a06-d281-44cd-836e-85ee9a6098a2", "prompt_length": 2190, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2811):**\nTitle: CARBS Terminal Migration on 05/12\nDescription: *Business Value:*  To Migrate F2F terminals for VISA & Mastercard merchants on CARBS\n\n*Acceptance Criteria:*\n\nF2F terminals with required details to migrate are provided in CSV format as required. see attached\n\n[^F2F_05122024.csv]\n\n...\n\n**Ticket B (MARS-2810):**\nTitle: enable cross-region access from AG txn svc to auth-amex\nDescription: [Diagram of current proposed solution|https://lucid.app/lucidchart/2989ef11-5d82-4780-bd19-e9f943a1fc5a/edit?invitationId=inv_77dd7e8b-eec0-4778-ac08-9f35da367c81&page=iN~w_5tMaDZC#]\n\n\n\nThe proposed solution for removing local cluster URLs is as follows:\n\n* Set up static IP for ALBs \n** This has been completed already in staging [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/frankfurt/gke/terragrunt.hcl#L53] and [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/london/gke/terragrunt.hcl#L54], still needs finishing in prod\n* Generate internal ALBs with static IPs\n** this is done [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ld-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L94] and [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ff-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L43]\n** Currently this is not working as expected - the internal ALBs need to be created as global ALBs, but there\u2019s no way to do this when creating them with k8s annotations, so this approach _may_ need to be rethought. [~accountid:5de502972fd6260cf27cdc0d] has requested guidance from David (04-12-24) - this ticket will be updated with any proposed changes. If David does not get back before Harry is on leave please whoever picks up this ticket follow up with David.\n* Update host alias in MARSGCP transaction services in AG + adjust endpoint URLs\n** This has been started for staging [here|https://github.com/dojo-engineering/auth-gateway/pull/4294] - until the ALB issue is resolved it cannot be properly tested so the PR remains in draft and unmerged.\n** *Note:* it was decided to retain the local cluster URLs for communication with services in the same cluster to keep things simple (e.g. AG MARSGCPLondon transaction service \u2192 auth-amex London should use a local cluster URL)\n*** that means we have to update MARSGCPLondon instance deployed in FF and MARSGCPFrankfurt instance deployed in London (2 configs) with IP addresses of above ALBs\n* Testing strategy\n** The current e2e testing strategy for this change is to use the [AGs end-to-end test tool|https://github.com/dojo-engineering/auth-gateway/tree/ce5410eb53950b19a70a44e5fbc8967ff3283d49/end-to-end-tests] to send requests to MARSGCPLondon in staging using the following command:\n*** {{./e2e payment staging-ld-purchase --processor=MARSGCPLondon --card-scheme=amex}}\n** [A GCP connectivity test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] ({{ldn-ff-peered-networking-test}}) has also been setup and may provide a faster feedback loop \n\n\n\n\n\nSoooo, I think I've found the issue, it basically boils down to the ILBs not being global, and there's no way for us to set that from k8s that I can tell. This is just something we're not going to be able to fix with the way we've currently got the ALBs setup\n\nThere's an [outstanding issue|https://issuetracker.google.com/issues/272577313] and something in the [k8s/ingress-gce|https://github.com/kubernetes/ingress-gce/issues/1887] repo suggesting that what we're trying to do is just not [http://possible.To|http://possible.To|smart-link]  speed up debugging I added this [ld-ff-peered-networking-test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] which is what pointed me in the direction in the first place\n\n* I've tried adding the {{networking.gke.io/internal-load-balancer-allow-global-access: \"true\"}} annotation to the service/ingress but it looks like this is just for l4 lbs - [https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|smart-link] \n* I've tried adding a [GCPGatewayPolicy|https://cloud.google.com/kubernetes-engine/docs/how-to/configure-gateway-resources] but these only apply to {{gateway.networking.k8s.io}} - not our\u00a0 Istio gateways\n* I tried manually creating a LB frontend with global access set to true, and that actually worked, but probably not something we'll want to do in prod (ss1)\n* I tried updating it with a gcloud command, that failed:\n\n{noformat}payments-infrastructure \ue7f0 (dojo-payments-nonprod) \uf418 test-global-lb-access \u276f gcloud beta compute forwarding-rules update k8s2-fs-8d36fjog-istio-ing-istio-ingress-v2-stg-auth-a-qy272bvf --region europe-west3 --allow-global-access                          \nERROR: (gcloud.beta.compute.forwarding-rules.update) Could not fetch resource:\n - Invalid value for field 'resource.allowGlobalAccess': 'true'. Updating the allow-global-access flag is only supported for regional INTERNAL forwarding rules with backend service/target instance.{noformat}\n\nin terms of potential fixes...\n\n* We could setup the global internal lb manually using [terraform|https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule#example-usage---forwarding-rule-global-internallb]\n* We could make the lb external? Although that seems like it shouldn't be necessary when we have cluster networking peering setup?\n* Switch to an l4 LB and use the annotation above?\n\n\n\n*Business Value:*\n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *KINDA*\n** F2F AG will do the release since it changes their config - hence the notification will happen and AG will be in context\n* Does it affect resources shared with Clearing? *NO*\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750512123.4816, "type": "request", "request_id": "444bdd2a-8731-4823-9c22-a0011a050a2d", "prompt_length": 2245, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2810):**\nTitle: enable cross-region access from AG txn svc to auth-amex\nDescription: [Diagram of current proposed solution|https://lucid.app/lucidchart/2989ef11-5d82-4780-bd19-e9f943a1fc5a/edit?invitationId=inv_77dd7e8b-eec0-4778-ac08-9f35da367c81&page=iN~w_5tMaDZC#]\n\n\n\nThe proposed solution for removing local cluster URLs is as follows:\n\n* Set up static IP for ALBs \n** This has been completed already in staging [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/frankfurt/gke/terragrunt.hcl#L53] and [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/london/gke/terragrunt.hcl#L54], still needs finishing in prod\n* Generate internal ALBs with static IPs\n** this is done [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ld-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L94] and [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ff-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L43]\n** Currently this is not working as expected - the internal ALBs need to be created as global ALBs, but there\u2019s no way to do this when creating them with k8s annotations, so this approach _may_ need to be rethought. [~accountid:5de502972fd6260cf27cdc0d] has requested guidance from David (04-12-24) - this ticket will be updated with any proposed changes. If David does not get back before Harry is on leave please whoever picks up this ticket follow up with David.\n* Update host alias in MARSGCP transaction services in AG + adjust endpoint URLs\n** This has been started for staging [here|https://github.com/dojo-engineering/auth-gateway/pull/4294] - until the ALB issue is resolved it cannot be properly tested so the PR remains in draft and unmerged.\n** *Note:* it was decided to retain the local cluster URLs for communication with services in the same cluster to keep things simple (e.g. AG MARSGCPLondon transaction service \u2192 auth-amex London should use a local cluster URL)\n*** that means we have to update MARSGCPLondon instance deployed in FF and MARSGCPFrankfurt instance deployed in London (2 configs) with IP addresses of above ALBs\n* Testing strategy\n** The current e2e testing strategy for this change is to use the [AGs end-to-end test tool|https://github.com/dojo-engineering/auth-gateway/tree/ce5410eb53950b19a70a44e5fbc8967ff3283d49/end-to-end-tests] to send requests to MARSGCPLondon in staging using the following command:\n*** {{./e2e payment staging-ld-purchase --processor=MARSGCPLondon --card-scheme=amex}}\n** [A GCP connectivity test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] ({{ldn-ff-peered-networking-test}}) has also been setup and may provide a faster feedback loop \n\n\n\n\n\nSoooo, I think I've found the issue, it basically boils down to the ILBs not being global, and there's no way for us to set that from k8s that I can tell. This is just something we're not going to be able to fix with the way we've currently got the ALBs setup\n\nThere's an [outstanding issue|https://issuetracker.google.com/issues/272577313] and something in the [k8s/ingress-gce|https://github.com/kubernetes/ingress-gce/issues/1887] repo suggesting that what we're trying to do is just not [http://possible.To|http://possible.To|smart-link]  speed up debugging I added this [ld-ff-peered-networking-test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] which is what pointed me in the direction in the first place\n\n* I've tried adding the {{networking.gke.io/internal-load-balancer-allow-global-access: \"true\"}} annotation to the service/ingress but it looks like this is just for l4 lbs - [https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|smart-link] \n* I've tried adding a [GCPGatewayPolicy|https://cloud.google.com/kubernetes-engine/docs/how-to/configure-gateway-resources] but these only apply to {{gateway.networking.k8s.io}} - not our\u00a0 Istio gateways\n* I tried manually creating a LB frontend with global access set to true, and that actually worked, but probably not something we'll want to do in prod (ss1)\n* I tried updating it with a gcloud command, that failed:\n\n{noformat}payments-infrastructure \ue7f0 (dojo-payments-nonprod) \uf418 test-global-lb-access \u276f gcloud beta compute forwarding-rules update k8s2-fs-8d36fjog-istio-ing-istio-ingress-v2-stg-auth-a-qy272bvf --region europe-west3 --allow-global-access                          \nERROR: (gcloud.beta.compute.forwarding-rules.update) Could not fetch resource:\n - Invalid value for field 'resource.allowGlobalAccess': 'true'. Updating the allow-global-access flag is only supported for regional INTERNAL forwarding rules with backend service/target instance.{noformat}\n\nin terms of potential fixes...\n\n* We could setup the global internal lb manually using [terraform|https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule#example-usage---forwarding-rule-global-internallb]\n* We could make the lb external? Although that seems like it shouldn't be necessary when we have cluster networking peering setup?\n* Switch to an l4 LB and use the annotation above?\n\n\n\n*Business Value:*\n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *KINDA*\n** F2F AG will do the release since it changes their config - hence the notification will happen and AG will be in context\n* Does it affect resources shared with Clearing? *NO*\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2814):**\nTitle: Migrate new terminals from already migrated merchants\nDescription: *Business Value:*\n\nNew terminals from merchants already migrated to CARBS Auth are onboarded with SV as the default processor. These should be migrated to CARBS\n\n[^F2F_All terminals_0612.csv] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*\n\n* Migrate new list of terminals...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750512204.2323048, "type": "request", "request_id": "961adb1a-7c55-4d75-838d-d9830eb135bc", "prompt_length": 2451, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2810):**\nTitle: enable cross-region access from AG txn svc to auth-amex\nDescription: [Diagram of current proposed solution|https://lucid.app/lucidchart/2989ef11-5d82-4780-bd19-e9f943a1fc5a/edit?invitationId=inv_77dd7e8b-eec0-4778-ac08-9f35da367c81&page=iN~w_5tMaDZC#]\n\n\n\nThe proposed solution for removing local cluster URLs is as follows:\n\n* Set up static IP for ALBs \n** This has been completed already in staging [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/frankfurt/gke/terragrunt.hcl#L53] and [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/london/gke/terragrunt.hcl#L54], still needs finishing in prod\n* Generate internal ALBs with static IPs\n** this is done [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ld-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L94] and [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ff-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L43]\n** Currently this is not working as expected - the internal ALBs need to be created as global ALBs, but there\u2019s no way to do this when creating them with k8s annotations, so this approach _may_ need to be rethought. [~accountid:5de502972fd6260cf27cdc0d] has requested guidance from David (04-12-24) - this ticket will be updated with any proposed changes. If David does not get back before Harry is on leave please whoever picks up this ticket follow up with David.\n* Update host alias in MARSGCP transaction services in AG + adjust endpoint URLs\n** This has been started for staging [here|https://github.com/dojo-engineering/auth-gateway/pull/4294] - until the ALB issue is resolved it cannot be properly tested so the PR remains in draft and unmerged.\n** *Note:* it was decided to retain the local cluster URLs for communication with services in the same cluster to keep things simple (e.g. AG MARSGCPLondon transaction service \u2192 auth-amex London should use a local cluster URL)\n*** that means we have to update MARSGCPLondon instance deployed in FF and MARSGCPFrankfurt instance deployed in London (2 configs) with IP addresses of above ALBs\n* Testing strategy\n** The current e2e testing strategy for this change is to use the [AGs end-to-end test tool|https://github.com/dojo-engineering/auth-gateway/tree/ce5410eb53950b19a70a44e5fbc8967ff3283d49/end-to-end-tests] to send requests to MARSGCPLondon in staging using the following command:\n*** {{./e2e payment staging-ld-purchase --processor=MARSGCPLondon --card-scheme=amex}}\n** [A GCP connectivity test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] ({{ldn-ff-peered-networking-test}}) has also been setup and may provide a faster feedback loop \n\n\n\n\n\nSoooo, I think I've found the issue, it basically boils down to the ILBs not being global, and there's no way for us to set that from k8s that I can tell. This is just something we're not going to be able to fix with the way we've currently got the ALBs setup\n\nThere's an [outstanding issue|https://issuetracker.google.com/issues/272577313] and something in the [k8s/ingress-gce|https://github.com/kubernetes/ingress-gce/issues/1887] repo suggesting that what we're trying to do is just not [http://possible.To|http://possible.To|smart-link]  speed up debugging I added this [ld-ff-peered-networking-test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] which is what pointed me in the direction in the first place\n\n* I've tried adding the {{networking.gke.io/internal-load-balancer-allow-global-access: \"true\"}} annotation to the service/ingress but it looks like this is just for l4 lbs - [https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|smart-link] \n* I've tried adding a [GCPGatewayPolicy|https://cloud.google.com/kubernetes-engine/docs/how-to/configure-gateway-resources] but these only apply to {{gateway.networking.k8s.io}} - not our\u00a0 Istio gateways\n* I tried manually creating a LB frontend with global access set to true, and that actually worked, but probably not something we'll want to do in prod (ss1)\n* I tried updating it with a gcloud command, that failed:\n\n{noformat}payments-infrastructure \ue7f0 (dojo-payments-nonprod) \uf418 test-global-lb-access \u276f gcloud beta compute forwarding-rules update k8s2-fs-8d36fjog-istio-ing-istio-ingress-v2-stg-auth-a-qy272bvf --region europe-west3 --allow-global-access                          \nERROR: (gcloud.beta.compute.forwarding-rules.update) Could not fetch resource:\n - Invalid value for field 'resource.allowGlobalAccess': 'true'. Updating the allow-global-access flag is only supported for regional INTERNAL forwarding rules with backend service/target instance.{noformat}\n\nin terms of potential fixes...\n\n* We could setup the global internal lb manually using [terraform|https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule#example-usage---forwarding-rule-global-internallb]\n* We could make the lb external? Although that seems like it shouldn't be necessary when we have cluster networking peering setup?\n* Switch to an l4 LB and use the annotation above?\n\n\n\n*Business Value:*\n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *KINDA*\n** F2F AG will do the release since it changes their config - hence the notification will happen and AG will be in context\n* Does it affect resources shared with Clearing? *NO*\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2813):**\nTitle: reduce STAN duplicates generated within same second for MC txns\nDescription: *Business Value:*\n\nMC requires STAN to be unique within the same second (same de7 value) regardless of PAN/MID/TID used.\n\n\n\n!image-20241206-133259.png|width=1628,height=490,alt=\"image-20241206-133259.png\"!\n\nRight now we\u2019re not exactly compliant with the above requirement, see Santander\u2019s complaint:\n\n\n\n!image-20241206-133328.png|width=2578,height=1512,alt=\"image-20241206-133328.png\"!\n\nRelated slack thread [https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|https://dojo.slack.com/archives/C068TCXJKEY/p1732786520198389|smart-link] \n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *YES / NO*\n** (if yes - notify AG/RAG/Clearing)\n* Does it affect resources shared with Clearing? *YES* / *NO*\n** (if yes - notify Clearing)\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750512292.0852659, "type": "request", "request_id": "a30c4bb9-7e9d-4bf0-b056-335ebc3aee44", "prompt_length": 2132, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2810):**\nTitle: enable cross-region access from AG txn svc to auth-amex\nDescription: [Diagram of current proposed solution|https://lucid.app/lucidchart/2989ef11-5d82-4780-bd19-e9f943a1fc5a/edit?invitationId=inv_77dd7e8b-eec0-4778-ac08-9f35da367c81&page=iN~w_5tMaDZC#]\n\n\n\nThe proposed solution for removing local cluster URLs is as follows:\n\n* Set up static IP for ALBs \n** This has been completed already in staging [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/frankfurt/gke/terragrunt.hcl#L53] and [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/london/gke/terragrunt.hcl#L54], still needs finishing in prod\n* Generate internal ALBs with static IPs\n** this is done [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ld-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L94] and [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ff-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L43]\n** Currently this is not working as expected - the internal ALBs need to be created as global ALBs, but there\u2019s no way to do this when creating them with k8s annotations, so this approach _may_ need to be rethought. [~accountid:5de502972fd6260cf27cdc0d] has requested guidance from David (04-12-24) - this ticket will be updated with any proposed changes. If David does not get back before Harry is on leave please whoever picks up this ticket follow up with David.\n* Update host alias in MARSGCP transaction services in AG + adjust endpoint URLs\n** This has been started for staging [here|https://github.com/dojo-engineering/auth-gateway/pull/4294] - until the ALB issue is resolved it cannot be properly tested so the PR remains in draft and unmerged.\n** *Note:* it was decided to retain the local cluster URLs for communication with services in the same cluster to keep things simple (e.g. AG MARSGCPLondon transaction service \u2192 auth-amex London should use a local cluster URL)\n*** that means we have to update MARSGCPLondon instance deployed in FF and MARSGCPFrankfurt instance deployed in London (2 configs) with IP addresses of above ALBs\n* Testing strategy\n** The current e2e testing strategy for this change is to use the [AGs end-to-end test tool|https://github.com/dojo-engineering/auth-gateway/tree/ce5410eb53950b19a70a44e5fbc8967ff3283d49/end-to-end-tests] to send requests to MARSGCPLondon in staging using the following command:\n*** {{./e2e payment staging-ld-purchase --processor=MARSGCPLondon --card-scheme=amex}}\n** [A GCP connectivity test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] ({{ldn-ff-peered-networking-test}}) has also been setup and may provide a faster feedback loop \n\n\n\n\n\nSoooo, I think I've found the issue, it basically boils down to the ILBs not being global, and there's no way for us to set that from k8s that I can tell. This is just something we're not going to be able to fix with the way we've currently got the ALBs setup\n\nThere's an [outstanding issue|https://issuetracker.google.com/issues/272577313] and something in the [k8s/ingress-gce|https://github.com/kubernetes/ingress-gce/issues/1887] repo suggesting that what we're trying to do is just not [http://possible.To|http://possible.To|smart-link]  speed up debugging I added this [ld-ff-peered-networking-test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] which is what pointed me in the direction in the first place\n\n* I've tried adding the {{networking.gke.io/internal-load-balancer-allow-global-access: \"true\"}} annotation to the service/ingress but it looks like this is just for l4 lbs - [https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|smart-link] \n* I've tried adding a [GCPGatewayPolicy|https://cloud.google.com/kubernetes-engine/docs/how-to/configure-gateway-resources] but these only apply to {{gateway.networking.k8s.io}} - not our\u00a0 Istio gateways\n* I tried manually creating a LB frontend with global access set to true, and that actually worked, but probably not something we'll want to do in prod (ss1)\n* I tried updating it with a gcloud command, that failed:\n\n{noformat}payments-infrastructure \ue7f0 (dojo-payments-nonprod) \uf418 test-global-lb-access \u276f gcloud beta compute forwarding-rules update k8s2-fs-8d36fjog-istio-ing-istio-ingress-v2-stg-auth-a-qy272bvf --region europe-west3 --allow-global-access                          \nERROR: (gcloud.beta.compute.forwarding-rules.update) Could not fetch resource:\n - Invalid value for field 'resource.allowGlobalAccess': 'true'. Updating the allow-global-access flag is only supported for regional INTERNAL forwarding rules with backend service/target instance.{noformat}\n\nin terms of potential fixes...\n\n* We could setup the global internal lb manually using [terraform|https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule#example-usage---forwarding-rule-global-internallb]\n* We could make the lb external? Although that seems like it shouldn't be necessary when we have cluster networking peering setup?\n* Switch to an l4 LB and use the annotation above?\n\n\n\n*Business Value:*\n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *KINDA*\n** F2F AG will do the release since it changes their config - hence the notification will happen and AG will be in context\n* Does it affect resources shared with Clearing? *NO*\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2812):**\nTitle: Create diners KafkaConnector in prod\nDescription: *Business Value:*\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750512364.939956, "type": "request", "request_id": "183c532e-11da-4f29-b51b-38782093b79e", "prompt_length": 2190, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2810):**\nTitle: enable cross-region access from AG txn svc to auth-amex\nDescription: [Diagram of current proposed solution|https://lucid.app/lucidchart/2989ef11-5d82-4780-bd19-e9f943a1fc5a/edit?invitationId=inv_77dd7e8b-eec0-4778-ac08-9f35da367c81&page=iN~w_5tMaDZC#]\n\n\n\nThe proposed solution for removing local cluster URLs is as follows:\n\n* Set up static IP for ALBs \n** This has been completed already in staging [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/frankfurt/gke/terragrunt.hcl#L53] and [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/london/gke/terragrunt.hcl#L54], still needs finishing in prod\n* Generate internal ALBs with static IPs\n** this is done [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ld-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L94] and [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ff-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L43]\n** Currently this is not working as expected - the internal ALBs need to be created as global ALBs, but there\u2019s no way to do this when creating them with k8s annotations, so this approach _may_ need to be rethought. [~accountid:5de502972fd6260cf27cdc0d] has requested guidance from David (04-12-24) - this ticket will be updated with any proposed changes. If David does not get back before Harry is on leave please whoever picks up this ticket follow up with David.\n* Update host alias in MARSGCP transaction services in AG + adjust endpoint URLs\n** This has been started for staging [here|https://github.com/dojo-engineering/auth-gateway/pull/4294] - until the ALB issue is resolved it cannot be properly tested so the PR remains in draft and unmerged.\n** *Note:* it was decided to retain the local cluster URLs for communication with services in the same cluster to keep things simple (e.g. AG MARSGCPLondon transaction service \u2192 auth-amex London should use a local cluster URL)\n*** that means we have to update MARSGCPLondon instance deployed in FF and MARSGCPFrankfurt instance deployed in London (2 configs) with IP addresses of above ALBs\n* Testing strategy\n** The current e2e testing strategy for this change is to use the [AGs end-to-end test tool|https://github.com/dojo-engineering/auth-gateway/tree/ce5410eb53950b19a70a44e5fbc8967ff3283d49/end-to-end-tests] to send requests to MARSGCPLondon in staging using the following command:\n*** {{./e2e payment staging-ld-purchase --processor=MARSGCPLondon --card-scheme=amex}}\n** [A GCP connectivity test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] ({{ldn-ff-peered-networking-test}}) has also been setup and may provide a faster feedback loop \n\n\n\n\n\nSoooo, I think I've found the issue, it basically boils down to the ILBs not being global, and there's no way for us to set that from k8s that I can tell. This is just something we're not going to be able to fix with the way we've currently got the ALBs setup\n\nThere's an [outstanding issue|https://issuetracker.google.com/issues/272577313] and something in the [k8s/ingress-gce|https://github.com/kubernetes/ingress-gce/issues/1887] repo suggesting that what we're trying to do is just not [http://possible.To|http://possible.To|smart-link]  speed up debugging I added this [ld-ff-peered-networking-test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] which is what pointed me in the direction in the first place\n\n* I've tried adding the {{networking.gke.io/internal-load-balancer-allow-global-access: \"true\"}} annotation to the service/ingress but it looks like this is just for l4 lbs - [https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|smart-link] \n* I've tried adding a [GCPGatewayPolicy|https://cloud.google.com/kubernetes-engine/docs/how-to/configure-gateway-resources] but these only apply to {{gateway.networking.k8s.io}} - not our\u00a0 Istio gateways\n* I tried manually creating a LB frontend with global access set to true, and that actually worked, but probably not something we'll want to do in prod (ss1)\n* I tried updating it with a gcloud command, that failed:\n\n{noformat}payments-infrastructure \ue7f0 (dojo-payments-nonprod) \uf418 test-global-lb-access \u276f gcloud beta compute forwarding-rules update k8s2-fs-8d36fjog-istio-ing-istio-ingress-v2-stg-auth-a-qy272bvf --region europe-west3 --allow-global-access                          \nERROR: (gcloud.beta.compute.forwarding-rules.update) Could not fetch resource:\n - Invalid value for field 'resource.allowGlobalAccess': 'true'. Updating the allow-global-access flag is only supported for regional INTERNAL forwarding rules with backend service/target instance.{noformat}\n\nin terms of potential fixes...\n\n* We could setup the global internal lb manually using [terraform|https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule#example-usage---forwarding-rule-global-internallb]\n* We could make the lb external? Although that seems like it shouldn't be necessary when we have cluster networking peering setup?\n* Switch to an l4 LB and use the annotation above?\n\n\n\n*Business Value:*\n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *KINDA*\n** F2F AG will do the release since it changes their config - hence the notification will happen and AG will be in context\n* Does it affect resources shared with Clearing? *NO*\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2811):**\nTitle: CARBS Terminal Migration on 05/12\nDescription: *Business Value:*  To Migrate F2F terminals for VISA & Mastercard merchants on CARBS\n\n*Acceptance Criteria:*\n\nF2F terminals with required details to migrate are provided in CSV format as required. see attached\n\n[^F2F_05122024.csv]\n\n...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
{"timestamp": 1750512442.1053581, "type": "request", "request_id": "2c3f5955-1684-49f4-a069-5c5bc9706a4b", "prompt_length": 3860, "prompt": "You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.\n\nCompare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:\n\n**Ticket A (MARS-2810):**\nTitle: enable cross-region access from AG txn svc to auth-amex\nDescription: [Diagram of current proposed solution|https://lucid.app/lucidchart/2989ef11-5d82-4780-bd19-e9f943a1fc5a/edit?invitationId=inv_77dd7e8b-eec0-4778-ac08-9f35da367c81&page=iN~w_5tMaDZC#]\n\n\n\nThe proposed solution for removing local cluster URLs is as follows:\n\n* Set up static IP for ALBs \n** This has been completed already in staging [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/frankfurt/gke/terragrunt.hcl#L53] and [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/london/gke/terragrunt.hcl#L54], still needs finishing in prod\n* Generate internal ALBs with static IPs\n** this is done [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ld-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L94] and [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ff-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L43]\n** Currently this is not working as expected - the internal ALBs need to be created as global ALBs, but there\u2019s no way to do this when creating them with k8s annotations, so this approach _may_ need to be rethought. [~accountid:5de502972fd6260cf27cdc0d] has requested guidance from David (04-12-24) - this ticket will be updated with any proposed changes. If David does not get back before Harry is on leave please whoever picks up this ticket follow up with David.\n* Update host alias in MARSGCP transaction services in AG + adjust endpoint URLs\n** This has been started for staging [here|https://github.com/dojo-engineering/auth-gateway/pull/4294] - until the ALB issue is resolved it cannot be properly tested so the PR remains in draft and unmerged.\n** *Note:* it was decided to retain the local cluster URLs for communication with services in the same cluster to keep things simple (e.g. AG MARSGCPLondon transaction service \u2192 auth-amex London should use a local cluster URL)\n*** that means we have to update MARSGCPLondon instance deployed in FF and MARSGCPFrankfurt instance deployed in London (2 configs) with IP addresses of above ALBs\n* Testing strategy\n** The current e2e testing strategy for this change is to use the [AGs end-to-end test tool|https://github.com/dojo-engineering/auth-gateway/tree/ce5410eb53950b19a70a44e5fbc8967ff3283d49/end-to-end-tests] to send requests to MARSGCPLondon in staging using the following command:\n*** {{./e2e payment staging-ld-purchase --processor=MARSGCPLondon --card-scheme=amex}}\n** [A GCP connectivity test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] ({{ldn-ff-peered-networking-test}}) has also been setup and may provide a faster feedback loop \n\n\n\n\n\nSoooo, I think I've found the issue, it basically boils down to the ILBs not being global, and there's no way for us to set that from k8s that I can tell. This is just something we're not going to be able to fix with the way we've currently got the ALBs setup\n\nThere's an [outstanding issue|https://issuetracker.google.com/issues/272577313] and something in the [k8s/ingress-gce|https://github.com/kubernetes/ingress-gce/issues/1887] repo suggesting that what we're trying to do is just not [http://possible.To|http://possible.To|smart-link]  speed up debugging I added this [ld-ff-peered-networking-test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] which is what pointed me in the direction in the first place\n\n* I've tried adding the {{networking.gke.io/internal-load-balancer-allow-global-access: \"true\"}} annotation to the service/ingress but it looks like this is just for l4 lbs - [https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|smart-link] \n* I've tried adding a [GCPGatewayPolicy|https://cloud.google.com/kubernetes-engine/docs/how-to/configure-gateway-resources] but these only apply to {{gateway.networking.k8s.io}} - not our\u00a0 Istio gateways\n* I tried manually creating a LB frontend with global access set to true, and that actually worked, but probably not something we'll want to do in prod (ss1)\n* I tried updating it with a gcloud command, that failed:\n\n{noformat}payments-infrastructure \ue7f0 (dojo-payments-nonprod) \uf418 test-global-lb-access \u276f gcloud beta compute forwarding-rules update k8s2-fs-8d36fjog-istio-ing-istio-ingress-v2-stg-auth-a-qy272bvf --region europe-west3 --allow-global-access                          \nERROR: (gcloud.beta.compute.forwarding-rules.update) Could not fetch resource:\n - Invalid value for field 'resource.allowGlobalAccess': 'true'. Updating the allow-global-access flag is only supported for regional INTERNAL forwarding rules with backend service/target instance.{noformat}\n\nin terms of potential fixes...\n\n* We could setup the global internal lb manually using [terraform|https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule#example-usage---forwarding-rule-global-internallb]\n* We could make the lb external? Although that seems like it shouldn't be necessary when we have cluster networking peering setup?\n* Switch to an l4 LB and use the annotation above?\n\n\n\n*Business Value:*\n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *KINDA*\n** F2F AG will do the release since it changes their config - hence the notification will happen and AG will be in context\n* Does it affect resources shared with Clearing? *NO*\n\n*Acceptance Criteria:*...\n\n**Ticket B (MARS-2810):**\nTitle: enable cross-region access from AG txn svc to auth-amex\nDescription: [Diagram of current proposed solution|https://lucid.app/lucidchart/2989ef11-5d82-4780-bd19-e9f943a1fc5a/edit?invitationId=inv_77dd7e8b-eec0-4778-ac08-9f35da367c81&page=iN~w_5tMaDZC#]\n\n\n\nThe proposed solution for removing local cluster URLs is as follows:\n\n* Set up static IP for ALBs \n** This has been completed already in staging [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/frankfurt/gke/terragrunt.hcl#L53] and [here|https://github.com/dojo-engineering/payments-infrastructure/blob/main/terraform/providers/google/dojo-payments-nonprod/nonprod/london/gke/terragrunt.hcl#L54], still needs finishing in prod\n* Generate internal ALBs with static IPs\n** this is done [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ld-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L94] and [here|https://github.com/dojo-engineering/payments-releases/blob/main/deployments/non-production/dojo-payments-nonprod/gke-nonprod-ff-1/cluster-services/istio-ingress-v2/istio-ingress-v2/values.yaml#L43]\n** Currently this is not working as expected - the internal ALBs need to be created as global ALBs, but there\u2019s no way to do this when creating them with k8s annotations, so this approach _may_ need to be rethought. [~accountid:5de502972fd6260cf27cdc0d] has requested guidance from David (04-12-24) - this ticket will be updated with any proposed changes. If David does not get back before Harry is on leave please whoever picks up this ticket follow up with David.\n* Update host alias in MARSGCP transaction services in AG + adjust endpoint URLs\n** This has been started for staging [here|https://github.com/dojo-engineering/auth-gateway/pull/4294] - until the ALB issue is resolved it cannot be properly tested so the PR remains in draft and unmerged.\n** *Note:* it was decided to retain the local cluster URLs for communication with services in the same cluster to keep things simple (e.g. AG MARSGCPLondon transaction service \u2192 auth-amex London should use a local cluster URL)\n*** that means we have to update MARSGCPLondon instance deployed in FF and MARSGCPFrankfurt instance deployed in London (2 configs) with IP addresses of above ALBs\n* Testing strategy\n** The current e2e testing strategy for this change is to use the [AGs end-to-end test tool|https://github.com/dojo-engineering/auth-gateway/tree/ce5410eb53950b19a70a44e5fbc8967ff3283d49/end-to-end-tests] to send requests to MARSGCPLondon in staging using the following command:\n*** {{./e2e payment staging-ld-purchase --processor=MARSGCPLondon --card-scheme=amex}}\n** [A GCP connectivity test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] ({{ldn-ff-peered-networking-test}}) has also been setup and may provide a faster feedback loop \n\n\n\n\n\nSoooo, I think I've found the issue, it basically boils down to the ILBs not being global, and there's no way for us to set that from k8s that I can tell. This is just something we're not going to be able to fix with the way we've currently got the ALBs setup\n\nThere's an [outstanding issue|https://issuetracker.google.com/issues/272577313] and something in the [k8s/ingress-gce|https://github.com/kubernetes/ingress-gce/issues/1887] repo suggesting that what we're trying to do is just not [http://possible.To|http://possible.To|smart-link]  speed up debugging I added this [ld-ff-peered-networking-test|https://console.cloud.google.com/net-intelligence/connectivity/tests/list?invt=AbjCGg&project=dojo-payments-nonprod] which is what pointed me in the direction in the first place\n\n* I've tried adding the {{networking.gke.io/internal-load-balancer-allow-global-access: \"true\"}} annotation to the service/ingress but it looks like this is just for l4 lbs - [https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|https://cloud.google.com/kubernetes-engine/docs/concepts/service-load-balancer-parameters#global_access_internal_lb|smart-link] \n* I've tried adding a [GCPGatewayPolicy|https://cloud.google.com/kubernetes-engine/docs/how-to/configure-gateway-resources] but these only apply to {{gateway.networking.k8s.io}} - not our\u00a0 Istio gateways\n* I tried manually creating a LB frontend with global access set to true, and that actually worked, but probably not something we'll want to do in prod (ss1)\n* I tried updating it with a gcloud command, that failed:\n\n{noformat}payments-infrastructure \ue7f0 (dojo-payments-nonprod) \uf418 test-global-lb-access \u276f gcloud beta compute forwarding-rules update k8s2-fs-8d36fjog-istio-ing-istio-ingress-v2-stg-auth-a-qy272bvf --region europe-west3 --allow-global-access                          \nERROR: (gcloud.beta.compute.forwarding-rules.update) Could not fetch resource:\n - Invalid value for field 'resource.allowGlobalAccess': 'true'. Updating the allow-global-access flag is only supported for regional INTERNAL forwarding rules with backend service/target instance.{noformat}\n\nin terms of potential fixes...\n\n* We could setup the global internal lb manually using [terraform|https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_forwarding_rule#example-usage---forwarding-rule-global-internallb]\n* We could make the lb external? Although that seems like it shouldn't be necessary when we have cluster networking peering setup?\n* Switch to an l4 LB and use the annotation above?\n\n\n\n*Business Value:*\n\n*Impact and notifications:*\n\n* Could this change cause global or regional downtime of our API? *KINDA*\n** F2F AG will do the release since it changes their config - hence the notification will happen and AG will be in context\n* Does it affect resources shared with Clearing? *NO*\n\n*Acceptance Criteria:*...\n\nConsider these factors:\n1. Functional similarity (same feature/component)\n2. Technical similarity (similar implementation approach)\n3. Domain similarity (same type of work)\n4. File overlap (if files are changed in similar areas)\n\nFocus on content similarity, paying less attention to structural similarity of ticket description.\nIt is expected that tickets from the same project (i.e. with the same key) have similar structure - this doesn't contribute to similarity.\nIt is also expected that we compare tickets from the same project, being from the same project (having the same key prefix) shouldn't contribute to similarity.\n\nScoring guidelines:\n- 1.0: Identical or nearly identical tickets or the same ticket mentioned twice\n- 0.8-0.9: Very similar functionality or implementation\n- 0.6-0.7: Related work in same component\n- 0.4-0.5: Some overlap but different focus\n- 0.2-0.3: Minimal similarity\n- 0.0-0.1: Completely unrelated\n\nIMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.\n\n{\n    \"similarity_score\": 0.75,\n    \"reasoning\": \"Brief explanation of the similarity assessment\"\n}", "model_preset": "gemma3", "temperature": 0.1, "top_p": 0.9, "max_tokens": 200, "repetition_penalty": 1.05, "repetition_context_size": 256}
