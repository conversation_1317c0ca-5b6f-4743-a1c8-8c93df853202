{"matrix": {"MARS-2814": {"MARS-2814": 0.95, "MARS-2813": 0.75, "MARS-2812": 0.75, "MARS-2811": 0.75, "MARS-2810": 0.75}, "MARS-2813": {"MARS-2814": 0.75, "MARS-2813": 0.95, "MARS-2812": 0.75, "MARS-2811": 0.75, "MARS-2810": 0.75}, "MARS-2812": {"MARS-2814": 0.75, "MARS-2813": 0.75, "MARS-2812": 0.95, "MARS-2811": 0.75, "MARS-2810": 0.75}, "MARS-2811": {"MARS-2814": 0.75, "MARS-2813": 0.75, "MARS-2812": 0.25, "MARS-2811": 0.95, "MARS-2810": 0.75}, "MARS-2810": {"MARS-2814": 0.75, "MARS-2813": 0.75, "MARS-2812": 0.75, "MARS-2811": 0.75, "MARS-2810": 0.95}}, "validation": {"is_valid": false, "self_comparison_scores": [0.95, 0.95, 0.95, 0.95, 0.95], "min_self_score": 0.95, "max_self_score": 0.95, "avg_self_score": 0.95, "symmetry_violations": ["MARS-2812:MARS-2811", "MARS-2811:MARS-2812"], "errors": ["Matrix symmetry validation failed"], "warnings": []}, "ticket_keys": ["MARS-2814", "MARS-2813", "MARS-2812", "MARS-2811", "MARS-2810"]}