#!/usr/bin/env python3
"""
Test script for MLX wrapper functionality.

This script tests the MLX wrapper to ensure it works correctly with
the established patterns and provides proper logging.
"""

import logging
import sys
from pathlib import Path

from mlx_wrapper import MLXWrapper


def test_basic_generation():
    """Test basic text generation with the MLX wrapper."""
    print("Testing basic MLX wrapper functionality...")
    
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        # Initialize wrapper with gemma3 model (recommended default)
        wrapper = MLXWrapper(
            model_preset="gemma3",
            log_dir=Path("logs/test_mlx"),
            logging_enabled=True
        )
        
        # Test simple prompt
        prompt = """You are a helpful assistant. Please respond with a simple JSON object.

/no_think

Provide a brief greeting in JSON format:

{
    "message": "Hello, world!",
    "status": "success"
}"""
        
        print("Generating response...")
        response = wrapper.generate(
            prompt=prompt,
            temperature=0.1,
            max_tokens=100
        )
        
        print(f"Response: {response}")
        print("✓ Basic generation test passed")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic generation test failed: {e}")
        return False


def test_similarity_pattern():
    """Test the similarity comparison pattern used in jira_similarity_matrix.py."""
    print("\nTesting similarity comparison pattern...")
    
    try:
        wrapper = MLXWrapper(
            model_preset="gemma3",
            log_dir=Path("logs/test_mlx"),
            logging_enabled=True
        )
        
        # Test similarity prompt pattern
        prompt = """You are an expert software engineer analyzing JIRA tickets. Your task is to compare two tickets and determine their similarity.

/no_think

Compare these two JIRA tickets and provide a similarity score from 0.0 to 1.0:

**Ticket A (TEST-001):**
Title: Add user authentication
Description: Implement user login and registration functionality

**Ticket B (TEST-002):**
Title: User login system
Description: Create authentication system for user access

Consider these factors:
1. Functional similarity (same feature/component)
2. Technical similarity (similar implementation approach)
3. Domain similarity (same type of work)

IMPORTANT: Return ONLY the JSON object below. Do not include any other text, explanations, or formatting.

{
    "similarity_score": 0.75,
    "reasoning": "Brief explanation of the similarity assessment"
}"""
        
        print("Generating similarity response...")
        response = wrapper.generate(
            prompt=prompt,
            temperature=0.1,
            top_p=0.9,
            max_tokens=200,
            repetition_penalty=1.05,
            repetition_context_size=256
        )
        
        print(f"Similarity response: {response}")
        
        # Try to parse as JSON to verify format
        import json
        try:
            # Extract JSON from response
            start_idx = response.find('{')
            if start_idx != -1:
                end_idx = response.rfind('}') + 1
                json_str = response[start_idx:end_idx]
                data = json.loads(json_str)
                
                if 'similarity_score' in data:
                    print(f"✓ Similarity pattern test passed - score: {data['similarity_score']}")
                    return True
                else:
                    print("✗ Similarity pattern test failed - no similarity_score in response")
                    return False
            else:
                print("✗ Similarity pattern test failed - no JSON found in response")
                return False
                
        except json.JSONDecodeError as e:
            print(f"✗ Similarity pattern test failed - invalid JSON: {e}")
            return False
        
    except Exception as e:
        print(f"✗ Similarity pattern test failed: {e}")
        return False


def test_logging():
    """Test that logging is working correctly."""
    print("\nTesting logging functionality...")
    
    try:
        log_dir = Path("logs/test_mlx")
        wrapper = MLXWrapper(
            model_preset="gemma3",
            log_dir=log_dir,
            logging_enabled=True
        )
        
        # Generate a simple response to create log entries
        response = wrapper.generate(
            prompt="Say hello in JSON format: {\"message\": \"hello\"}",
            temperature=0.1,
            max_tokens=50
        )
        
        # Check if log files were created
        requests_log = log_dir / "mlx_requests.jsonl"
        responses_log = log_dir / "mlx_responses.jsonl"
        
        if requests_log.exists() and responses_log.exists():
            print("✓ Log files created successfully")
            
            # Check if log files have content
            with open(requests_log, 'r') as f:
                request_lines = f.readlines()
            with open(responses_log, 'r') as f:
                response_lines = f.readlines()
                
            if request_lines and response_lines:
                print(f"✓ Logging test passed - {len(request_lines)} requests, {len(response_lines)} responses logged")
                return True
            else:
                print("✗ Logging test failed - log files are empty")
                return False
        else:
            print("✗ Logging test failed - log files not created")
            return False
            
    except Exception as e:
        print(f"✗ Logging test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("MLX Wrapper Test Suite")
    print("=" * 50)
    
    tests = [
        test_basic_generation,
        test_similarity_pattern,
        test_logging
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! MLX wrapper is working correctly.")
        sys.exit(0)
    else:
        print("✗ Some tests failed. Please check the output above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
