# JIRA Ticket Similarity Matrix Generator

This script generates a similarity matrix for JIRA tickets using an MLX-based LLM, following the patterns established in `idea_refiner.py` and `translate3_mlx_2.py`.

## Features

- **MLX Integration**: Uses local MLX models with JSON-only output and `/no_think` directive
- **Comprehensive Analysis**: Compares ticket descriptions and optionally file changes
- **Matrix Validation**: Ensures self-comparison scores ≥0.95 and matrix symmetry
- **Progress Tracking**: Shows progress for O(N²) comparisons with ETA
- **Caching**: Supports caching to resume interrupted runs
- **Multiple Outputs**: Generates both JSON and CSV formats
- **Robust Error Handling**: Retry logic and validation checks

## Requirements

- Python 3.8+
- MLX framework (`mlx-lm` package)
- Local MLX model (Gemma3, Qwen3, or ALMA13)
- `enriched_tickets_clean.json` file with JIRA tickets

## Installation

```bash
# Install MLX (if not already installed)
pip install mlx-lm

# Optional: Install tqdm for better progress tracking
pip install tqdm
```

## Usage

### Basic Usage

```bash
# Generate similarity matrix with default settings
python jira_similarity_matrix.py

# Use specific model preset
python jira_similarity_matrix.py --model-preset qwen3

# Exclude file changes from similarity calculation
python jira_similarity_matrix.py --no-include-files

# Use custom input/output files
python jira_similarity_matrix.py \
    --tickets my_tickets.json \
    --output-json my_matrix.json \
    --output-csv my_matrix.csv
```

### Advanced Options

```bash
# Enable caching for large datasets
python jira_similarity_matrix.py --cache-file similarity_cache.json

# Adjust LLM parameters
python jira_similarity_matrix.py \
    --temperature 0.05 \
    --max-retries 3

# Debug mode
python jira_similarity_matrix.py --log-level DEBUG
```

### Testing

```bash
# Run quick test with 5 tickets
python test_similarity_matrix.py

# Test with more tickets
python test_similarity_matrix.py --tickets 10

# Test different model
python test_similarity_matrix.py --model alma13

# Analyze existing results
python test_similarity_matrix.py --analyze-only
```

## Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--tickets` | Path to enriched JIRA tickets JSON file | `enriched_tickets_clean.json` |
| `--output-json` | Output path for JSON similarity matrix | `similarity_matrix_output.json` |
| `--output-csv` | Output path for CSV similarity matrix | `similarity_matrix_output.csv` |
| `--model-preset` | MLX model preset (gemma3/qwen3) | `gemma3` |
| `--include-files` | Include file changes in similarity calculation | `True` |
| `--no-include-files` | Exclude file changes from similarity calculation | - |
| `--temperature` | LLM sampling temperature (lower = more consistent) | `0.1` |
| `--max-retries` | Maximum retries for failed LLM calls | `2` |
| `--cache-file` | Path to cache file for storing similarity scores | None |
| `--log-level` | Logging level (DEBUG/INFO/WARNING/ERROR) | `INFO` |

## Output Format

### JSON Output
```json
{
  "matrix": {
    "MARS-2814": {
      "MARS-2814": 1.0,
      "MARS-2813": 0.25,
      "MARS-2812": 0.15
    },
    "MARS-2813": {
      "MARS-2814": 0.25,
      "MARS-2813": 1.0,
      "MARS-2812": 0.30
    }
  },
  "metadata": {
    "n_tickets": 3,
    "total_comparisons": 9,
    "generation_time_seconds": 45.2,
    "model_preset": "qwen3",
    "include_files": true,
    "validation": {
      "overall_passed": true,
      "self_comparison_check": {...},
      "symmetry_check": {...},
      "score_range_check": {...},
      "completeness_check": {...}
    }
  },
  "ticket_keys": ["MARS-2814", "MARS-2813", "MARS-2812"]
}
```

### CSV Output
```csv
ticket_key,MARS-2814,MARS-2813,MARS-2812
MARS-2814,1.000,0.250,0.150
MARS-2813,0.250,1.000,0.300
MARS-2812,0.150,0.300,1.000
```

## Validation Checks

The script performs several validation checks:

1. **Self-Comparison**: Tickets compared to themselves should score ≥0.95
2. **Symmetry**: similarity(A,B) should ≈ similarity(B,A) (tolerance: 0.05)
3. **Score Range**: All scores should be within [0.0, 1.0]
4. **Completeness**: Matrix should contain N×N comparisons

## Performance Considerations

- **Large Datasets**: Use `--cache-file` to enable resuming interrupted runs
- **Model Choice**:
  - `gemma3`: **Recommended default** - Excellent reliability and JSON compliance (8K tokens)
  - `qwen3`: Faster but unreliable JSON parsing, not recommended (32K tokens)
- **Memory Usage**: Each comparison uses ~200 tokens, plan accordingly
- **Time Estimates**: ~2-5 seconds per comparison depending on model

## Troubleshooting

### Common Issues

1. **MLX Not Available**
   ```bash
   pip install mlx-lm
   ```

2. **Model Not Found**
   - Check model paths in `PRESETS` dictionary
   - Ensure models are downloaded to specified locations

3. **Out of Memory**
   - Use gemma3 (recommended default)
   - Reduce batch size in code
   - Process tickets in smaller chunks

4. **Validation Failures**
   - Check LLM responses for consistency
   - Adjust temperature (lower = more consistent)
   - Review failed comparisons in debug logs

### Debug Mode

Enable debug logging to see detailed LLM interactions:

```bash
python jira_similarity_matrix.py --log-level DEBUG
```

## Integration with Existing Workflow

This script follows the same patterns as other tools in the repository:

- **MLX Configuration**: Same model presets and loading patterns as `translate3_mlx_2.py`
- **JSON Output**: Structured output with validation like `idea_refiner.py`
- **Error Handling**: Robust retry logic and graceful degradation
- **Progress Tracking**: Compatible with existing monitoring approaches

## Example Workflow

```bash
# 1. Test with small dataset first
python test_similarity_matrix.py --tickets 10

# 2. Run on full dataset with caching
python jira_similarity_matrix.py \
    --cache-file similarity_cache.json \
    --model-preset qwen3

# 3. Analyze results
python test_similarity_matrix.py --analyze-only

# 4. Use results for further analysis
python -c "
import json
with open('similarity_matrix_output.json') as f:
    data = json.load(f)
    matrix = data['matrix']
    # Your analysis code here
"
```

## Technical Implementation

The script implements the following key components:

- **SimilarityMatrixGenerator**: Main class handling the generation process
- **MLX Integration**: Model loading with repetition penalty (1.05, context size 256)
- **JSON Schema Validation**: Ensures LLM returns valid similarity scores
- **Caching System**: Stores computed similarities to enable resuming
- **Progress Tracking**: Shows completion percentage and ETA
- **Validation Framework**: Comprehensive checks for matrix correctness

The similarity calculation considers:
- Functional similarity (same feature/component)
- Technical similarity (similar implementation approach)  
- Domain similarity (same payment scheme, same type of work)
- File overlap (if files are changed in similar areas)

Scoring guidelines:
- 1.0: Identical or nearly identical tickets
- 0.8-0.9: Very similar functionality or implementation
- 0.6-0.7: Related work in same domain/component
- 0.4-0.5: Some overlap but different focus
- 0.2-0.3: Minimal similarity
- 0.0-0.1: Completely unrelated
