#!/usr/bin/env python3
"""
Shared Type Definitions for JIRA Ticket Processing

This module contains strongly-typed data structures shared between
jira_similarity_matrix.py and idea_refiner.py to improve type safety
and reduce Dict[str, Any] usage throughout the codebase.
"""

import json
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Set, Any


class ChangeType(Enum):
    """File change types from git."""
    MODIFIED = "M"
    ADDED = "A"
    DELETED = "D"
    RENAMED = "R"


class PaymentScheme(Enum):
    """Payment processing schemes."""
    VISA = "visa"
    MASTERCARD = "mc"
    AMEX = "amex"
    JCB = "jcb"
    DINERS = "diners"


@dataclass
class FileChange:
    """Represents a file change in a git commit."""
    file_path: str
    change_type: ChangeType

    @classmethod
    def from_dict(cls, data: Dict[str, str]) -> 'FileChange':
        """Create FileChange from dictionary representation."""
        return cls(
            file_path=data["file_path"],
            change_type=ChangeType(data["change_type"])
        )

    def to_dict(self) -> Dict[str, str]:
        """Convert to dictionary representation."""
        return {
            "file_path": self.file_path,
            "change_type": self.change_type.value
        }


@dataclass
class JiraTicket:
    """Core JIRA ticket structure."""
    key: str
    title: str
    description: str

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'JiraTicket':
        """Create JiraTicket from dictionary representation."""
        return cls(
            key=data["key"],
            title=data["title"],
            description=data["description"]
        )

    def to_dict(self) -> Dict[str, str]:
        """Convert to dictionary representation."""
        return {
            "key": self.key,
            "title": self.title,
            "description": self.description
        }


@dataclass
class EnrichedJiraTicket:
    """JIRA ticket with file change information."""
    key: str
    title: str
    description: str
    files_changed: List[FileChange] = field(default_factory=list)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EnrichedJiraTicket':
        """Create EnrichedJiraTicket from dictionary representation."""
        files_changed = []
        if "files_changed" in data:
            files_changed = [
                FileChange.from_dict(fc) if isinstance(fc, dict) else fc
                for fc in data["files_changed"]
            ]

        return cls(
            key=data["key"],
            title=data["title"],
            description=data["description"],
            files_changed=files_changed
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "key": self.key,
            "title": self.title,
            "description": self.description,
            "files_changed": [fc.to_dict() for fc in self.files_changed]
        }

    def to_base_ticket(self) -> JiraTicket:
        """Convert to base JiraTicket."""
        return JiraTicket(
            key=self.key,
            title=self.title,
            description=self.description
        )


@dataclass
class MLXConfig:
    """Configuration for MLX model usage."""
    model_preset: str
    model_path: str
    max_context: int
    temperature: float = 0.1
    max_tokens: int = 200
    repetition_penalty: float = 1.05
    repetition_context_size: int = 256


# MLX Model presets configuration
MLX_PRESETS: Dict[str, MLXConfig] = {
    "gemma3": MLXConfig(
        model_preset="gemma3",
        model_path="/Users/<USER>/.lmstudio/models/mlx-community/gemma-3-27b-it-qat-4bit",
        max_context=8192
    ),
    "qwen3": MLXConfig(
        model_preset="qwen3",
        model_path="/Users/<USER>/.lmstudio/models/lmstudio-community/Qwen3-30B-A3B-MLX-4bit",
        max_context=32768
    ),
}


@dataclass
class ValidationResult:
    """Result of validation operations."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "is_valid": self.is_valid,
            "errors": self.errors,
            "warnings": self.warnings,
            "metadata": self.metadata
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ValidationResult':
        """Create from dictionary representation."""
        return cls(
            is_valid=data["is_valid"],
            errors=data.get("errors", []),
            warnings=data.get("warnings", []),
            metadata=data.get("metadata", {})
        )


@dataclass
class ContextMetadata:
    """Metadata about context used in LLM processing."""
    similar_tickets_count: int = 0
    scheme_patterns_used: List[PaymentScheme] = field(default_factory=list)
    file_patterns_count: int = 0
    architectural_components_used: List[str] = field(default_factory=list)
    total_context_tokens: int = 0
    context_truncated: bool = False

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "similar_tickets_count": self.similar_tickets_count,
            "scheme_patterns_used": [scheme.value for scheme in self.scheme_patterns_used],
            "file_patterns_count": self.file_patterns_count,
            "architectural_components_used": self.architectural_components_used,
            "total_context_tokens": self.total_context_tokens,
            "context_truncated": self.context_truncated
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ContextMetadata':
        """Create from dictionary representation."""
        scheme_patterns = []
        if "scheme_patterns_used" in data:
            scheme_patterns = [PaymentScheme(scheme) for scheme in data["scheme_patterns_used"]]

        return cls(
            similar_tickets_count=data.get("similar_tickets_count", 0),
            scheme_patterns_used=scheme_patterns,
            file_patterns_count=data.get("file_patterns_count", 0),
            architectural_components_used=data.get("architectural_components_used", []),
            total_context_tokens=data.get("total_context_tokens", 0),
            context_truncated=data.get("context_truncated", False)
        )


@dataclass
class SimilarityMatrix:
    """Complete similarity matrix with metadata."""
    matrix: Dict[str, Dict[str, float]]
    validation: 'MatrixValidationResult'
    ticket_keys: List[str]

    def get_similarity(self, key_a: str, key_b: str) -> Optional[float]:
        """Get similarity score between two tickets."""
        return self.matrix.get(key_a, {}).get(key_b)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "matrix": self.matrix,
            "validation": self.validation.to_dict(),
            "ticket_keys": self.ticket_keys
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SimilarityMatrix':
        """Create from dictionary representation."""
        validation = MatrixValidationResult.from_dict(data["validation"])
        return cls(
            matrix=data["matrix"],
            validation=validation,
            ticket_keys=data["ticket_keys"]
        )


@dataclass
class MatrixValidationResult:
    """Result of similarity matrix validation."""
    is_valid: bool
    self_comparison_scores: List[float]
    min_self_score: float
    max_self_score: float
    avg_self_score: float
    symmetry_violations: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "is_valid": self.is_valid,
            "self_comparison_scores": self.self_comparison_scores,
            "min_self_score": self.min_self_score,
            "max_self_score": self.max_self_score,
            "avg_self_score": self.avg_self_score,
            "symmetry_violations": self.symmetry_violations,
            "errors": self.errors,
            "warnings": self.warnings
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MatrixValidationResult':
        """Create from dictionary representation."""
        return cls(
            is_valid=data["is_valid"],
            self_comparison_scores=data.get("self_comparison_scores", []),
            min_self_score=data.get("min_self_score", 0.0),
            max_self_score=data.get("max_self_score", 0.0),
            avg_self_score=data.get("avg_self_score", 0.0),
            symmetry_violations=data.get("symmetry_violations", []),
            errors=data.get("errors", []),
            warnings=data.get("warnings", [])
        )


@dataclass
class LLMSimilarityResponse:
    """Structured response from LLM similarity comparison."""
    similarity_score: float
    reasoning: Optional[str] = None
    confidence: Optional[float] = None
    factors_considered: List[str] = field(default_factory=list)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LLMSimilarityResponse':
        """Create from dictionary response."""
        return cls(
            similarity_score=float(data["similarity_score"]),
            reasoning=data.get("reasoning"),
            confidence=data.get("confidence"),
            factors_considered=data.get("factors_considered", [])
        )


@dataclass
class LLMRefinementResponse:
    """Structured response from LLM idea refinement."""
    title: str
    business_value: str
    description: str
    acceptance_criteria: List[str]
    open_questions: List[str]
    suggested_design: str
    likely_files: List[str]
    impact_assessment: str
    confidence: Optional[float] = None

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'LLMRefinementResponse':
        """Create from dictionary response."""
        return cls(
            title=data["title"],
            business_value=data["business_value"],
            description=data["description"],
            acceptance_criteria=data.get("acceptance_criteria", []),
            open_questions=data.get("open_questions", []),
            suggested_design=data.get("suggested_design", ""),
            likely_files=data.get("likely_files", []),
            impact_assessment=data.get("impact_assessment", ""),
            confidence=data.get("confidence")
        )


# Utility functions for JSON serialization
def serialize_context_for_json(context: Dict[str, Any]) -> Dict[str, Any]:
    """Convert context with typed objects to JSON-serializable format."""
    serializable = {}

    for key, value in context.items():
        if key == 'similar_tickets':
            # Handle ContextualTicket objects
            from typing import TYPE_CHECKING
            if TYPE_CHECKING:
                pass

            serializable[key] = []
            for ctx_ticket in value:
                if hasattr(ctx_ticket, 'ticket') and hasattr(ctx_ticket, 'similarity_score'):
                    serializable[key].append({
                        'ticket': ctx_ticket.ticket.to_dict(),
                        'similarity_score': ctx_ticket.similarity_score,
                        'file_changes': ctx_ticket.file_changes
                    })
                else:
                    # Fallback for dict-like objects
                    serializable[key].append(value)

        elif key in ['domain_examples', 'scheme_examples']:
            # Handle EnrichedJiraTicket objects
            serializable[key] = []
            for ticket in value:
                if hasattr(ticket, 'to_dict'):
                    serializable[key].append(ticket.to_dict())
                else:
                    # Fallback for dict-like objects
                    serializable[key].append(ticket)

        elif isinstance(value, list):
            # Handle lists of typed objects
            serializable[key] = []
            for item in value:
                if hasattr(item, 'to_dict'):
                    serializable[key].append(item.to_dict())
                else:
                    serializable[key].append(item)

        elif hasattr(value, 'to_dict'):
            # Handle single typed objects
            serializable[key] = value.to_dict()
        else:
            # Handle primitive types
            serializable[key] = value

    return serializable


def serialize_refined_idea_for_json(refined_idea) -> Dict[str, Any]:
    """Convert RefinedIdea to JSON-serializable format."""
    return {
        "original_idea": refined_idea.original_idea,
        "title": refined_idea.title,
        "business_value": refined_idea.business_value,
        "description": refined_idea.description,
        "acceptance_criteria": refined_idea.acceptance_criteria,
        "open_questions": refined_idea.open_questions,
        "suggested_design": refined_idea.suggested_design,
        "likely_files": refined_idea.likely_files,
        "impact_assessment": refined_idea.impact_assessment,
        "related_tickets": refined_idea.related_tickets,
        "context_metadata": refined_idea.context_used.to_dict(),
        "llm_response": refined_idea.llm_response.to_dict() if refined_idea.llm_response else None
    }


@dataclass
class IntelligentContext:
    """Strongly-typed context for LLM processing."""
    similar_tickets: List['ContextualTicket'] = field(default_factory=list)
    domain_examples: List[EnrichedJiraTicket] = field(default_factory=list)
    scheme_examples: List[EnrichedJiraTicket] = field(default_factory=list)
    file_patterns: List[str] = field(default_factory=list)
    components: List[str] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return serialize_context_for_json({
            'similar_tickets': self.similar_tickets,
            'domain_examples': self.domain_examples,
            'scheme_examples': self.scheme_examples,
            'file_patterns': self.file_patterns,
            'components': self.components
        })

    def estimate_token_count(self, tokenizer_func) -> int:
        """Estimate token count for this context."""
        context_text = json.dumps(self.to_dict(), indent=2)
        return tokenizer_func(context_text)


@dataclass
class ProjectKnowledge:
    """Container for all project knowledge extracted from input files."""
    tickets: List[JiraTicket]
    enriched_tickets: List[EnrichedJiraTicket]
    project_structure: List[str]
    git_commits: List[str]
    scheme_patterns: Dict[PaymentScheme, List[EnrichedJiraTicket]]
    file_change_patterns: Dict[str, List[str]]
    architectural_components: Set[str]
    domain_examples: Dict[str, List[EnrichedJiraTicket]]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "tickets": [ticket.to_dict() for ticket in self.tickets],
            "enriched_tickets": [ticket.to_dict() for ticket in self.enriched_tickets],
            "project_structure": self.project_structure,
            "git_commits": self.git_commits,
            "scheme_patterns": {
                scheme.value: [ticket.to_dict() for ticket in tickets]
                for scheme, tickets in self.scheme_patterns.items()
            },
            "file_change_patterns": self.file_change_patterns,
            "architectural_components": list(self.architectural_components),
            "domain_examples": {
                domain: [ticket.to_dict() for ticket in tickets]
                for domain, tickets in self.domain_examples.items()
            }
        }


@dataclass
class ContextualTicket:
    """A ticket with similarity score for context ranking."""
    ticket: EnrichedJiraTicket
    similarity_score: float
    file_changes: List[str]

    @classmethod
    def from_dict_ticket(cls, ticket_dict: Dict[str, Any], similarity_score: float) -> 'ContextualTicket':
        """Create ContextualTicket from dictionary ticket representation."""
        enriched_ticket = EnrichedJiraTicket.from_dict(ticket_dict)
        file_changes = [fc.file_path for fc in enriched_ticket.files_changed]
        return cls(
            ticket=enriched_ticket,
            similarity_score=similarity_score,
            file_changes=file_changes
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "ticket": self.ticket.to_dict(),
            "similarity_score": self.similarity_score,
            "file_changes": self.file_changes
        }


@dataclass
class RefinedIdea:
    """Container for a refined idea with LLM-generated content."""
    original_idea: str
    title: str
    business_value: str
    description: str
    acceptance_criteria: List[str]
    open_questions: List[str]
    suggested_design: str
    likely_files: List[str]
    impact_assessment: str
    related_tickets: List[str]
    context_used: ContextMetadata
    llm_response: Optional[LLMRefinementResponse] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return serialize_refined_idea_for_json(self)


@dataclass
class SimilarityResult:
    """Result of similarity comparison between two tickets."""
    key_a: str
    key_b: str
    similarity_score: float
    reasoning: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "key_a": self.key_a,
            "key_b": self.key_b,
            "similarity_score": self.similarity_score,
            "reasoning": self.reasoning
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SimilarityResult':
        """Create from dictionary representation."""
        return cls(
            key_a=data["key_a"],
            key_b=data["key_b"],
            similarity_score=data["similarity_score"],
            reasoning=data.get("reasoning")
        )
